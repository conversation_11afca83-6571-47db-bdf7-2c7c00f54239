import logging
import os

os.makedirs('logs', exist_ok=True)

def setup_logging():
    level = getattr(logging, 'WARNING', logging.WARNING)

    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/bot.log'),
            logging.StreamHandler()
        ]
    )
    logging.getLogger('discord.ext.commands.bot').setLevel(logging.ERROR)

    important_logger = logging.getLogger('important')
    important_logger.setLevel(logging.WARNING)

    important_handler = logging.FileHandler('logs/important.log')
    important_handler.setFormatter(
        logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    )
    important_logger.addHandler(important_handler)

    return important_logger

important_logger = setup_logging()
