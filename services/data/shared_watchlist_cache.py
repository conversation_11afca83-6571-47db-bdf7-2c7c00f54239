import logging
import time
import threading
from typing import Dict, Any
from services.market.market_service import get_market_service
from services.data.cache_service import get_cache

logger = logging.getLogger(__name__)

class SharedWatchlistCache:
    def __init__(self):
        self.market_service = get_market_service()
        self.cache = get_cache('shared_watchlist', ttl=60)  # 1 minute cache
        self.lock = threading.RLock()
        self.last_update = 0
        self.update_interval = 30  # 30 seconds minimum between updates
        
    async def get_watchlist_data(self) -> Dict[str, Any]:
        """Get cached watchlist data, refresh if needed"""
        try:
            current_time = time.time()
            
            with self.lock:
                # Check if we need to refresh
                if current_time - self.last_update < self.update_interval:
                    cached_data = self.cache.get('watchlist_data')
                    if cached_data:
                        logger.debug("Returning cached watchlist data")
                        return cached_data
                
                # Update last_update time to prevent concurrent updates
                self.last_update = current_time
            
            # Fetch fresh data
            logger.info("Fetching fresh watchlist data")
            watchlist_data = await self.market_service.get_watchlist_data()
            
            if watchlist_data['success']:
                # Cache the data
                with self.lock:
                    self.cache.set('watchlist_data', watchlist_data, ttl=60)
                    logger.debug(f"Cached watchlist data for {len(watchlist_data.get('prices', {}))} symbols")
            
            return watchlist_data
            
        except Exception as e:
            logger.error(f"Error getting shared watchlist data: {e}")
            return {
                'success': False,
                'error': str(e),
                'prices': {}
            }
    
    async def get_comprehensive_market_data(self, symbols: list, change_type: str = "24h") -> Dict[str, Any]:
        """Get comprehensive market data for symbols with caching"""
        try:
            cache_key = f"comprehensive_market_{hash(tuple(sorted(symbols)))}_{change_type}"
            
            # Check cache first
            cached_data = self.cache.get(cache_key)
            if cached_data:
                logger.debug(f"Returning cached comprehensive market data for {len(symbols)} symbols")
                return cached_data
            
            # Fetch fresh data
            logger.info(f"Fetching fresh comprehensive market data for {len(symbols)} symbols")
            
            # Use the existing method from Discord watchlist
            from handlers.discord.market.watchlist_commands import WatchlistCommands
            temp_watchlist = WatchlistCommands(None)
            temp_watchlist.market_service = self.market_service
            
            market_data = await temp_watchlist._fetch_comprehensive_market_data(symbols, change_type)
            
            # Cache the data
            self.cache.set(cache_key, market_data, ttl=60)
            logger.debug(f"Cached comprehensive market data for {len(symbols)} symbols")
            
            return market_data
            
        except Exception as e:
            logger.error(f"Error getting comprehensive market data: {e}")
            return {}
    
    def clear_cache(self):
        """Clear all cached data"""
        with self.lock:
            self.cache.clear()
            self.last_update = 0
            logger.info("Cleared shared watchlist cache")

# Global instance
_shared_watchlist_cache = None
_cache_lock = threading.RLock()

def get_shared_watchlist_cache() -> SharedWatchlistCache:
    """Get the global shared watchlist cache instance"""
    global _shared_watchlist_cache
    with _cache_lock:
        if _shared_watchlist_cache is None:
            _shared_watchlist_cache = SharedWatchlistCache()
        return _shared_watchlist_cache
