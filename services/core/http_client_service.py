import asyncio
import logging
import aiohttp
from typing import Optional, Dict, Union
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

class HTTPClientService:
    def __init__(self):
        self._session: Optional[aiohttp.ClientSession] = None
        self._connector: Optional[aiohttp.TCPConnector] = None
        self._lock = asyncio.Lock()

        self.timeout = aiohttp.ClientTimeout(total=30, connect=10)
        self.connector_config = {
            'limit': 100,
            'limit_per_host': 30,
            'ttl_dns_cache': 300,
            'use_dns_cache': True,
            'keepalive_timeout': 30,
            'enable_cleanup_closed': True
        }

        logger.info("HTTP Client Service initialized")

    async def _ensure_session(self) -> aiohttp.ClientSession:
        if self._session is None or self._session.closed:
            async with self._lock:
                if self._session is None or self._session.closed:
                    await self._create_session()
        return self._session

    async def _create_session(self):
        if self._connector:
            await self._connector.close()

        self._connector = aiohttp.TCPConnector(**self.connector_config)
        self._session = aiohttp.ClientSession(
            connector=self._connector,
            timeout=self.timeout,
            headers={'User-Agent': 'ChartFix-Bot/1.0'}
        )
        logger.info("Created new HTTP session with connection pooling")

    async def get(self, url: str, params: Optional[Dict] = None,
                  headers: Optional[Dict] = None, timeout: Optional[float] = None) -> aiohttp.ClientResponse:
        session = await self._ensure_session()
        request_timeout = aiohttp.ClientTimeout(total=timeout) if timeout else self.timeout

        return await session.get(url, params=params, headers=headers, timeout=request_timeout)

    async def post(self, url: str, data: Optional[Union[Dict, str]] = None,
                   json: Optional[Dict] = None, headers: Optional[Dict] = None,
                   timeout: Optional[float] = None) -> aiohttp.ClientResponse:
        session = await self._ensure_session()
        request_timeout = aiohttp.ClientTimeout(total=timeout) if timeout else self.timeout

        return await session.post(url, data=data, json=json, headers=headers, timeout=request_timeout)

    @asynccontextmanager
    async def request(self, method: str, url: str, **kwargs):
        session = await self._ensure_session()
        async with session.request(method, url, **kwargs) as response:
            yield response

    async def close(self):
        """Close HTTP session and connector with proper cleanup"""
        try:
            if self._session and not self._session.closed:
                await self._session.close()
                # Give time for connections to close properly
                await asyncio.sleep(0.25)
                logger.info("HTTP session closed")

            if self._connector:
                await self._connector.close()
                # Give time for connector to close properly
                await asyncio.sleep(0.25)
                logger.info("HTTP connector closed")

        except Exception as e:
            logger.error(f"Error during HTTP client cleanup: {e}")
        finally:
            self._session = None
            self._connector = None

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()

_http_client_service: Optional[HTTPClientService] = None
_client_lock = asyncio.Lock()

async def get_http_client() -> HTTPClientService:
    global _http_client_service
    if _http_client_service is None:
        async with _client_lock:
            if _http_client_service is None:
                _http_client_service = HTTPClientService()
    return _http_client_service

async def close_http_client():
    """Close the global HTTP client service with proper cleanup"""
    global _http_client_service
    if _http_client_service:
        try:
            await _http_client_service.close()
            logger.info("HTTP client service shutdown completed")
        except Exception as e:
            logger.error(f"Error during HTTP client shutdown: {e}")
        finally:
            _http_client_service = None

    # Additional cleanup to ensure all aiohttp connections are closed
    try:
        # Give extra time for any remaining connections to close
        await asyncio.sleep(0.1)
        logger.debug("HTTP client cleanup completed")
    except Exception as e:
        logger.error(f"Error during final HTTP cleanup: {e}")
