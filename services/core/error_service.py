import logging
import time
import random
import asyncio
import threading
import traceback
from typing import Optional, List, Dict, Any, Callable, TypeVar, Type
from functools import wraps

logger = logging.getLogger(__name__)

# Type variables for generic functions
T = TypeVar('T')
R = TypeVar('R')

class ServiceError(Exception):
    """Base class for all service errors."""

    def __init__(self, message: str, original_error: Optional[Exception] = None):
        self.message = message
        self.original_error = original_error
        super().__init__(self.message)

    def __str__(self) -> str:
        if self.original_error:
            return f"{self.message} (Caused by: {type(self.original_error).__name__}: {self.original_error})"
        return self.message


class NetworkError(ServiceError):
    """Error related to network connectivity."""
    pass


class APIError(ServiceError):
    """Error related to API calls."""
    pass


class ValidationError(ServiceError):
    """Error related to data validation."""
    pass


class TimeoutError(ServiceError):
    """Error related to operation timeouts."""
    pass


class NotFoundError(ServiceError):
    """Error related to resource not found."""
    pass


class ErrorService:
    """
    Dịch vụ xử lý lỗi thống nhất cho toàn bộ hệ thống.
    Cung cấp các phương thức để xử lý, ghi log và định dạng lỗi.
    """

    def __init__(self):
        # Cấu hình mặc định - minimal cho production
        self.default_max_retries = 1
        self.default_initial_backoff = 1.0
        self.default_max_backoff = 4.0
        self.default_exception_types = [NetworkError, TimeoutError]



    def handle_service_errors(self, func: Callable[..., T]) -> Callable[..., T]:
        """
        Decorator để xử lý lỗi cho các hàm dịch vụ.

        Args:
            func: Hàm cần xử lý lỗi

        Returns:
            Hàm đã được wrap
        """
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except ServiceError:
                # Ghi log và truyền lại lỗi
                self._log_error(func.__name__, sys_exc_info=True)
                raise
            except Exception as e:
                # Chuyển đổi lỗi thành ServiceError
                self._log_error(func.__name__, e, sys_exc_info=True)
                raise ServiceError(f"Service error in {func.__name__}: {e}", e)
        return wrapper

    def handle_service_errors_async(self, func: Callable[..., T]) -> Callable[..., T]:
        """
        Decorator để xử lý lỗi cho các hàm dịch vụ bất đồng bộ.

        Args:
            func: Hàm cần xử lý lỗi

        Returns:
            Hàm đã được wrap
        """
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except ServiceError:
                # Ghi log và truyền lại lỗi
                self._log_error(func.__name__, sys_exc_info=True)
                raise
            except Exception as e:
                # Chuyển đổi lỗi thành ServiceError
                self._log_error(func.__name__, e, sys_exc_info=True)
                raise ServiceError(f"Service error in {func.__name__}: {e}", e)
        return wrapper

    def retry_with_backoff(self, max_retries: int = None, initial_backoff: float = None,
                          max_backoff: float = None, exception_types: List[Type[Exception]] = None):
        """
        Decorator để thử lại các hàm với backoff theo cấp số nhân.

        Args:
            max_retries: Số lần thử lại tối đa
            initial_backoff: Thời gian chờ ban đầu (giây)
            max_backoff: Thời gian chờ tối đa (giây)
            exception_types: Danh sách các loại lỗi cần thử lại

        Returns:
            Decorator function
        """
        # Sử dụng giá trị mặc định nếu không được chỉ định
        max_retries = max_retries or self.default_max_retries
        initial_backoff = initial_backoff or self.default_initial_backoff
        max_backoff = max_backoff or self.default_max_backoff
        exception_types = exception_types or self.default_exception_types

        def decorator(func: Callable[..., R]) -> Callable[..., R]:
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                backoff = initial_backoff
                for retry in range(max_retries + 1):
                    try:
                        return await func(*args, **kwargs)
                    except Exception as e:
                        # Kiểm tra xem lỗi có thuộc loại cần thử lại không
                        should_retry = not exception_types or any(isinstance(e, t) for t in exception_types)

                        # Nếu đã hết số lần thử lại hoặc không phải loại lỗi cần thử lại
                        if not should_retry or retry == max_retries:
                            self._log_error(func.__name__, e, retry_count=retry)
                            raise

                        # Tính toán thời gian chờ với jitter
                        sleep_time = backoff + random.uniform(0, 0.1 * backoff)
                        logger.warning(f"Retry {retry+1}/{max_retries} for {func.__name__}: {e}")



                        # Chờ trước khi thử lại
                        await asyncio.sleep(sleep_time)

                        # Tăng thời gian chờ theo cấp số nhân
                        backoff = min(backoff * 2, max_backoff)

            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                backoff = initial_backoff
                for retry in range(max_retries + 1):
                    try:
                        return func(*args, **kwargs)
                    except Exception as e:
                        # Kiểm tra xem lỗi có thuộc loại cần thử lại không
                        should_retry = not exception_types or any(isinstance(e, t) for t in exception_types)

                        # Nếu đã hết số lần thử lại hoặc không phải loại lỗi cần thử lại
                        if not should_retry or retry == max_retries:
                            self._log_error(func.__name__, e, retry_count=retry)
                            raise

                        # Tính toán thời gian chờ với jitter
                        sleep_time = backoff + random.uniform(0, 0.1 * backoff)
                        logger.warning(f"Retry {retry+1}/{max_retries} for {func.__name__}: {e}")



                        # Chờ trước khi thử lại
                        time.sleep(sleep_time)

                        # Tăng thời gian chờ theo cấp số nhân
                        backoff = min(backoff * 2, max_backoff)

            # Chọn wrapper phù hợp dựa trên loại hàm
            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

        return decorator

    def get_error_message(self, error: Exception) -> str:
        """
        Lấy thông báo lỗi đơn giản cho một exception.

        Args:
            error: Exception cần lấy thông báo

        Returns:
            Thông báo lỗi
        """
        # Định nghĩa các thông báo lỗi đơn giản
        error_messages = {
            NetworkError: "🌐 Network error",
            APIError: "🔌 Service error",
            TimeoutError: "⏱️ Timeout",
            ValidationError: lambda e: f"⚠️ {str(e)}",
            NotFoundError: lambda e: f"🔍 {str(e)}"
        }

        # Tìm thông báo phù hợp
        for error_type, message in error_messages.items():
            if isinstance(error, error_type):
                return message(error) if callable(message) else message

        # Thông báo mặc định
        return f"❌ {str(error)}"

    def format_error_message(self, error: Exception, context: Dict[str, Any] = None) -> str:
        """
        Định dạng thông báo lỗi với ngữ cảnh.

        Args:
            error: Exception cần định dạng
            context: Ngữ cảnh bổ sung

        Returns:
            Thông báo lỗi đã định dạng
        """
        error_type = type(error).__name__
        error_message = str(error)

        # Lấy emoji phù hợp
        emoji_map = {
            "NetworkError": "🌐", "TimeoutError": "⏱️", "APIError": "🔌",
            "ValidationError": "⚠️", "NotFoundError": "🔍", "KeyError": "🔑",
            "ValueError": "⚠️", "ConnectionError": "🌐", "RequestError": "📡"
        }
        emoji = emoji_map.get(error_type, "❌")

        # Xây dựng thông báo cơ bản
        message = f"{emoji} "

        # Thêm ngữ cảnh nếu có
        if context:
            if "command" in context:
                message += f"Error executing /{context['command']}: "
            if "provider" in context:
                message += f"[{context['provider']}] "

        # Thêm thông báo lỗi
        message += error_message

        return message

    def _log_error(self, function_name: str, error: Exception = None,
                  retry_count: int = 0, sys_exc_info: bool = False) -> None:
        """
        Ghi log lỗi với thông tin chi tiết.

        Args:
            function_name: Tên hàm gặp lỗi
            error: Exception gặp phải
            retry_count: Số lần đã thử lại
            sys_exc_info: Có ghi log thông tin exception từ sys không
        """
        # Xây dựng thông báo lỗi
        message = f"Error in {function_name}"
        if retry_count > 0:
            message += f" after {retry_count} retries"
        if error:
            message += f": {error}"

        # Ghi log với mức độ phù hợp
        if sys_exc_info:
            logger.error(message, exc_info=True)
        else:
            logger.error(message)

        # Ghi lại stack trace cho debug
        if error:
            logger.debug(f"Stack trace for {function_name}: {traceback.format_exc()}")




# Singleton instance
_error_service = None
_instance_lock = threading.RLock()

def get_error_service() -> ErrorService:
    """Lấy instance singleton của ErrorService."""
    global _error_service
    with _instance_lock:
        if _error_service is None:
            _error_service = ErrorService()
        return _error_service

# Utility functions for service error handling
def handle_service_errors(func: Callable[..., T]) -> Callable[..., T]:
    """
    Decorator để xử lý lỗi cho các hàm dịch vụ.

    Args:
        func: Hàm cần xử lý lỗi

    Returns:
        Hàm đã được wrap
    """
    service = get_error_service()
    return service.handle_service_errors(func)

def handle_service_errors_async(func: Callable[..., T]) -> Callable[..., T]:
    """
    Decorator để xử lý lỗi cho các hàm dịch vụ bất đồng bộ.

    Args:
        func: Hàm cần xử lý lỗi

    Returns:
        Hàm đã được wrap
    """
    service = get_error_service()
    return service.handle_service_errors_async(func)

def retry_with_backoff(max_retries: int = None, initial_backoff: float = None,
                      max_backoff: float = None, exception_types: List[Type[Exception]] = None):
    """
    Decorator để thử lại các hàm với backoff theo cấp số nhân.

    Args:
        max_retries: Số lần thử lại tối đa
        initial_backoff: Thời gian chờ ban đầu (giây)
        max_backoff: Thời gian chờ tối đa (giây)
        exception_types: Danh sách các loại lỗi cần thử lại

    Returns:
        Decorator function
    """
    service = get_error_service()
    return service.retry_with_backoff(
        max_retries=max_retries,
        initial_backoff=initial_backoff,
        max_backoff=max_backoff,
        exception_types=exception_types
    )

def get_error_message(error: Exception) -> str:
    """
    Lấy thông báo lỗi đơn giản cho một exception.

    Args:
        error: Exception cần lấy thông báo

    Returns:
        Thông báo lỗi
    """
    service = get_error_service()
    return service.get_error_message(error)

def format_ai_error_message(error: Exception) -> str:
    """
    Định dạng thông báo lỗi cho AI.

    Args:
        error: Exception cần định dạng

    Returns:
        Thông báo lỗi đã định dạng
    """
    service = get_error_service()
    return service.format_error_message(error, {"provider": "AI"})


