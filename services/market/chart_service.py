import pandas as pd
import mplfinance as mpf
import matplotlib.pyplot as plt
import io
import logging
import numpy as np
import threading
import asyncio
from typing import Optional, List

from services.data.cache_service import get_cache_service
from services.market.market_service import get_market_service

logger = logging.getLogger(__name__)

class ChartService:
    """Service for generating financial charts with technical indicators"""

    def __init__(self):
        self.cache_service = get_cache_service()
        self.market_service = get_market_service()

        self.cache_lock = threading.RLock()

        self.supported_timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']
        self.default_timeframe = '1h'
        self.chart_cache_ttl = 300

        self.custom_style = self._create_chart_style()

        logger.info("ChartService initialized")

    def _create_chart_style(self):
        """Create custom chart style with dark theme"""
        return mpf.make_mpf_style(
            base_mpl_style='dark_background',
            rc={
                'axes.labelcolor': 'white',
                'axes.labelsize': 10,
                'xtick.color': 'white',
                'ytick.color': 'white',
                'axes.titlecolor': 'white',
                'axes.titlesize': 14,
                'axes.titleweight': 'bold',
                'legend.fontsize': 8,
                'figure.facecolor': '#121212',
                'axes.facecolor': '#1e1e1e',
                'axes.grid': True,
                'axes.grid.axis': 'both',
                'grid.color': '#444444',
                'grid.linestyle': ':',
                'grid.alpha': 0.4,
            },
            marketcolors={
                'candle': {'up': '#26a69a', 'down': '#ef5350'},
                'edge': {'up': '#00897b', 'down': '#d32f2f'},
                'wick': {'up': '#00897b', 'down': '#d32f2f'},
                'ohlc': {'up': '#00897b', 'down': '#d32f2f'},
                'volume': {'up': '#26a69a', 'down': '#ef5350'},
                'vcedge': {'up': '#26a69a', 'down': '#ef5350'},
                'alpha': 0.9
            }
        )

    async def generate_chart(self, symbol: str, timeframe: str = None,
                           chart_type: str = 'candlestick',
                           indicators: List[str] = None) -> Optional[io.BytesIO]:
        """
        Generate financial chart for given symbol and timeframe

        Args:
            symbol: Trading symbol (e.g., BTCUSDT)
            timeframe: Time interval (1m, 5m, 15m, 1h, 4h, 1d, 1w)
            chart_type: Chart type (candlestick, line)
            indicators: List of indicators to include (ema, volume)

        Returns:
            BytesIO buffer containing chart image or None if error
        """
        try:
            if timeframe is None:
                timeframe = self.default_timeframe

            if indicators is None:
                indicators = ['ema', 'volume']

            if timeframe not in self.supported_timeframes:
                logger.error(f"Unsupported timeframe: {timeframe}")
                return None

            cache_key = f"chart_{symbol}_{timeframe}_{chart_type}_{'_'.join(sorted(indicators))}"

            with self.cache_lock:
                cached_chart = self.cache_service.get(cache_key)
                if cached_chart:
                    logger.debug(f"Returning cached chart for {symbol} {timeframe}")
                    return cached_chart

            df = await self._fetch_ohlcv_data(symbol, timeframe)
            if df is None or df.empty:
                logger.error(f"No data available for {symbol} {timeframe}")
                return None

            chart_buffer = await self._generate_chart_image(df, symbol, timeframe, chart_type, indicators)

            if chart_buffer:
                with self.cache_lock:
                    self.cache_service.set(cache_key, chart_buffer, ttl=self.chart_cache_ttl)
                logger.info(f"Generated chart for {symbol} {timeframe}")

            return chart_buffer

        except Exception as e:
            logger.error(f"Error generating chart for {symbol} {timeframe}: {e}")
            return None

    async def _fetch_ohlcv_data(self, symbol: str, timeframe: str, limit: int = 200) -> Optional[pd.DataFrame]:
        """Fetch OHLCV data for chart generation"""
        try:
            loop = asyncio.get_event_loop()
            df = await loop.run_in_executor(None, self.market_service._fetch_ohlcv_data, symbol, timeframe, limit)

            if df is None or df.empty:
                return None

            required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in df.columns:
                    logger.error(f"DataFrame missing column {col}")
                    return None

            return df

        except Exception as e:
            logger.error(f"Error fetching OHLCV data for {symbol} {timeframe}: {e}")
            return None

    async def _generate_chart_image(self, df: pd.DataFrame, symbol: str, timeframe: str,
                                  chart_type: str, indicators: List[str]) -> Optional[io.BytesIO]:
        """Generate chart image from OHLCV data"""
        try:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, self._create_chart_sync, df, symbol, timeframe, chart_type, indicators)
        except Exception as e:
            logger.error(f"Error generating chart image: {e}")
            return None

    def _create_chart_sync(self, df: pd.DataFrame, symbol: str, timeframe: str,
                          chart_type: str, indicators: List[str]) -> Optional[io.BytesIO]:
        """Synchronous chart creation (runs in thread pool)"""
        try:
            df = df.copy()
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)

            addplots = []

            if 'ema' in indicators:
                df['EMA_8'] = df['close'].ewm(span=8, adjust=False).mean()
                df['EMA_21'] = df['close'].ewm(span=21, adjust=False).mean()
                df['EMA_200'] = df['close'].ewm(span=200, adjust=False).mean()

                addplots.extend([
                    mpf.make_addplot(df['EMA_8'], color='#ffcc00', width=1, label='EMA 8'),
                    mpf.make_addplot(df['EMA_21'], color='#cc3333', width=1, label='EMA 21'),
                    mpf.make_addplot(df['EMA_200'], color='#3366cc', width=1.5, label='EMA 200'),
                ])

                df['fill_green'] = np.where(df['EMA_8'] > df['EMA_21'], df['EMA_8'], np.nan)
                fill_green = dict(
                    y1=df['EMA_8'].values,
                    y2=df['EMA_21'].values,
                    where=df['EMA_8'] > df['EMA_21'],
                    alpha=0.4,
                    color='#00cc66'
                )
                addplots.append(mpf.make_addplot(df['fill_green'], type='line', fill_between=fill_green, alpha=0))

                df['fill_red'] = np.where(df['EMA_8'] < df['EMA_21'], df['EMA_8'], np.nan)
                fill_red = dict(
                    y1=df['EMA_8'].values,
                    y2=df['EMA_21'].values,
                    where=df['EMA_8'] < df['EMA_21'],
                    alpha=0.4,
                    color='#cc3333'
                )
                addplots.append(mpf.make_addplot(df['fill_red'], type='line', fill_between=fill_red, alpha=0))

            if 'volume' in indicators:
                df['MA10_volume'] = df['volume'].rolling(window=10, min_periods=1).mean()
                df['volume_color'] = '#999999'
                df.loc[df['volume'] > df['MA10_volume'] * 1.8, 'volume_color'] = '#cc3333'
                df.loc[df['volume'] > df['MA10_volume'] * 2.2, 'volume_color'] = '#800080'

                volume_plot = mpf.make_addplot(
                    df['volume'],
                    type='bar',
                    panel=1,
                    color=df['volume_color'].values,
                    ylabel='Volume',
                    alpha=0.9,
                    width=0.8
                )
                addplots.append(volume_plot)

                ma10_volume_plot = mpf.make_addplot(
                    df['MA10_volume'],
                    panel=1,
                    color='#ffffff',
                    width=1,
                    label='MA10 Volume'
                )
                addplots.append(ma10_volume_plot)

            latest_close = df['close'].iloc[-1]
            title = f"{symbol} {timeframe} | Last: ${latest_close:,.2f}"

            buffer = io.BytesIO()

            panel_ratios = (4, 1) if 'volume' in indicators else (1,)

            fig, _ = mpf.plot(
                df,
                type=chart_type,
                style=self.custom_style,
                title=title,
                addplot=addplots if addplots else None,
                figscale=1.3,
                savefig=dict(fname=buffer, dpi=150, bbox_inches='tight'),
                volume=False,
                panel_ratios=panel_ratios,
                show_nontrading=False,
                tight_layout=True,
                returnfig=True
            )

            fig.text(
                0.99, 0.01,
                'ChartFix Bot',
                alpha=0.4,
                color='white',
                fontsize=8,
                ha='right'
            )

            fig.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            plt.close(fig)
            buffer.seek(0)
            return buffer

        except Exception as e:
            logger.error(f"Error in _create_chart_sync: {e}")
            return None

    def is_valid_timeframe(self, timeframe: str) -> bool:
        """Check if timeframe is supported"""
        return timeframe in self.supported_timeframes

    def get_supported_timeframes(self) -> List[str]:
        """Get list of supported timeframes"""
        return self.supported_timeframes.copy()

_chart_service = None
_instance_lock = threading.RLock()

def get_chart_service() -> ChartService:
    """Get singleton instance of ChartService"""
    global _chart_service
    with _instance_lock:
        if _chart_service is None:
            _chart_service = ChartService()
        return _chart_service
