"""
Short-term Price Alert Service - Monitors 1H and 4H price changes for watchlist symbols
"""

import logging
import asyncio
import time
from datetime import datetime, timezone
from typing import Dict, List, Any, Callable, Optional
from dataclasses import dataclass

from utils.config import load_config
from services.market.percentage_calculation_service import get_percentage_service, CalculationMethod
from services.market.market_service import get_binance_futures_exchange

logger = logging.getLogger(__name__)

@dataclass
class ShortTermAlert:
    """Data class for short-term price alerts"""
    symbol: str
    timeframe: str  # '1h' or '4h'
    current_price: float
    percentage_change: float
    threshold: float
    direction: str  # 'up' or 'down'
    timestamp: datetime

class ShortTermPriceAlertService:
    """Service for monitoring short-term price changes (1H, 4H) for watchlist symbols"""
    
    def __init__(self):
        """Initialize the short-term price alert service"""
        self.percentage_service = get_percentage_service()
        self.exchange = None
        
        # Configuration
        self.enabled = False
        self.monitoring_interval = 300  # 5 minutes default
        self.timeframe_configs = {}  # {'1h': {'enabled': bool, 'thresholds': [...]}, ...}
        
        # Alert tracking to prevent spam
        self.recent_alerts = {}  # {symbol_timeframe_threshold: timestamp}
        self.alert_cooldown = 3600  # 1 hour cooldown for same alert

        # Alert callbacks
        self.alert_callbacks = []
        
        # Load configuration
        self._load_config()
        
        # Initialize exchange
        self._init_exchange()
    
    def _load_config(self):
        """Load configuration from config.yaml"""
        try:
            config = load_config()
            price_alerts_config = config.get('price_alerts', {})
            short_term_config = price_alerts_config.get('short_term_alerts', {})
            
            self.enabled = short_term_config.get('enabled', False)
            self.monitoring_interval = short_term_config.get('monitoring_interval', 300)
            
            # Load timeframe configurations
            timeframes_config = short_term_config.get('timeframes', {})
            self.timeframe_configs = {
                timeframe: {
                    'enabled': timeframes_config.get(timeframe, {}).get('enabled', False),
                    'thresholds': timeframes_config.get(timeframe, {}).get('thresholds', [])
                }
                for timeframe in ['1h', '4h']
            }
            
            logger.info(f"Short-term price alerts config loaded: enabled={self.enabled}")
            logger.info(f"Timeframe configs: {self.timeframe_configs}")
            
        except Exception as e:
            logger.error(f"Error loading short-term price alert config: {e}")
            self.enabled = False
    
    def _init_exchange(self):
        """Initialize Binance exchange connection"""
        try:
            self.exchange = get_binance_futures_exchange()
            logger.info("Binance exchange initialized for short-term price alerts")
        except Exception as e:
            logger.error(f"Error initializing exchange for short-term alerts: {e}")
            self.enabled = False
    
    def add_alert_callback(self, callback: Callable):
        """Add callback function for alert notifications"""
        self.alert_callbacks.append(callback)
    
    def get_watchlist_symbols(self) -> List[str]:
        """Get current watchlist symbols from config"""
        try:
            config = load_config()
            market_config = config.get('market', {})
            symbols = market_config.get('watchlist_symbols', [])
            logger.debug(f"Retrieved watchlist symbols: {symbols}")
            return symbols
        except Exception as e:
            logger.error(f"Error getting watchlist symbols: {e}")
            return []
    
    async def fetch_ohlcv_data(self, symbol: str, timeframe: str, limit: int = 2) -> List[Dict]:
        """Fetch OHLCV data for price change calculation"""
        try:
            if not self.exchange:
                return []
            
            # Convert timeframe format
            tf_map = {'1h': '1h', '4h': '4h'}
            exchange_timeframe = tf_map.get(timeframe, timeframe)
            
            # Fetch OHLCV data
            ohlcv = self.exchange.fetch_ohlcv(symbol, exchange_timeframe, limit=limit)
            
            if not ohlcv or len(ohlcv) < 2:
                return []
            
            # Convert to dict format
            data = []
            for candle in ohlcv:
                data.append({
                    'timestamp': candle[0],
                    'open': candle[1],
                    'high': candle[2],
                    'low': candle[3],
                    'close': candle[4],
                    'volume': candle[5]
                })
            
            return data
            
        except Exception as e:
            logger.error(f"Error fetching OHLCV data for {symbol} {timeframe}: {e}")
            return []
    
    async def calculate_price_change(self, symbol: str, timeframe: str) -> Optional[Dict[str, Any]]:
        """Calculate price change for given symbol and timeframe"""
        try:
            # Fetch OHLCV data
            ohlcv_data = await self.fetch_ohlcv_data(symbol, timeframe, limit=2)
            
            if len(ohlcv_data) < 2:
                logger.debug(f"Insufficient OHLCV data for {symbol} {timeframe}")
                return None
            
            # Get current and previous prices
            current_candle = ohlcv_data[-1]  # Latest candle
            previous_candle = ohlcv_data[-2]  # Previous candle
            
            current_price = current_candle['close']
            previous_price = previous_candle['close']
            
            # Calculate percentage change using unified service
            result = self.percentage_service.calculate_percentage_change(
                current_price, 
                previous_price,
                method=CalculationMethod.ROLLING_24H
            )
            
            if not result.is_valid:
                logger.warning(f"Invalid percentage calculation for {symbol} {timeframe}")
                return None
            
            return {
                'symbol': symbol,
                'timeframe': timeframe,
                'current_price': current_price,
                'previous_price': previous_price,
                'percentage_change': result.value,
                'timestamp': datetime.fromtimestamp(current_candle['timestamp'] / 1000, tz=timezone.utc)
            }
            
        except Exception as e:
            logger.error(f"Error calculating price change for {symbol} {timeframe}: {e}")
            return None
    
    def should_send_alert(self, symbol: str, timeframe: str, threshold: float) -> bool:
        """Check if alert should be sent (cooldown mechanism)"""
        alert_key = f"{symbol}_{timeframe}_{threshold}"
        current_time = time.time()
        
        if alert_key in self.recent_alerts:
            last_alert_time = self.recent_alerts[alert_key]
            if current_time - last_alert_time < self.alert_cooldown:
                return False
        
        # Update last alert time
        self.recent_alerts[alert_key] = current_time
        return True
    
    async def check_price_movements(self):
        """Check price movements for all watchlist symbols and timeframes"""
        if not self.enabled:
            return
        
        try:
            watchlist_symbols = self.get_watchlist_symbols()
            if not watchlist_symbols:
                logger.debug("No watchlist symbols to monitor")
                return
            
            alerts_to_send = []
            
            # Check each symbol and timeframe
            for symbol in watchlist_symbols:
                for timeframe, config in self.timeframe_configs.items():
                    if not config['enabled'] or not config['thresholds']:
                        continue
                    
                    # Calculate price change
                    price_data = await self.calculate_price_change(symbol, timeframe)
                    if not price_data:
                        continue
                    
                    percentage_change = price_data['percentage_change']
                    abs_change = abs(percentage_change)
                    
                    # Check against thresholds
                    for threshold in config['thresholds']:
                        if abs_change >= threshold:
                            # Check cooldown
                            if self.should_send_alert(symbol, timeframe, threshold):
                                direction = 'up' if percentage_change > 0 else 'down'
                                
                                alert = ShortTermAlert(
                                    symbol=symbol,
                                    timeframe=timeframe,
                                    current_price=price_data['current_price'],
                                    percentage_change=percentage_change,
                                    threshold=threshold,
                                    direction=direction,
                                    timestamp=price_data['timestamp']
                                )
                                
                                alerts_to_send.append(alert)
                                logger.info(f"Short-term alert triggered: {symbol} {timeframe} {percentage_change:.2f}% (>{threshold}%)")
                                break  # Only send one alert per symbol/timeframe
            
            # Send alerts
            for alert in alerts_to_send:
                await self._send_alert(alert)
                
        except Exception as e:
            logger.error(f"Error checking short-term price movements: {e}")
    
    async def _send_alert(self, alert: ShortTermAlert):
        """Send alert to registered callbacks"""
        try:
            alert_data = {
                'symbol': alert.symbol,
                'timeframe': alert.timeframe,
                'current_price': alert.current_price,
                'percentage_change': alert.percentage_change,
                'threshold': alert.threshold,
                'direction': alert.direction,
                'timestamp': alert.timestamp
            }
            
            # Call all registered callbacks
            for callback in self.alert_callbacks:
                try:
                    await callback('short_term_price_change', alert_data)
                except Exception as e:
                    logger.error(f"Error in alert callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error sending short-term alert: {e}")
    
    async def start_monitoring(self):
        """Start the monitoring loop"""
        if not self.enabled:
            logger.info("Short-term price alerts are disabled")
            return
        
        logger.info("Starting short-term price alert monitoring")
        
        while self.enabled:
            try:
                await self.check_price_movements()
                await asyncio.sleep(self.monitoring_interval)
            except Exception as e:
                logger.error(f"Error in short-term price alert monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    def stop_monitoring(self):
        """Stop the monitoring"""
        self.enabled = False
        logger.info("Short-term price alert monitoring stopped")

# Global service instance
_short_term_alert_service = None

def get_short_term_alert_service() -> ShortTermPriceAlertService:
    """Get global short-term price alert service instance"""
    global _short_term_alert_service
    if _short_term_alert_service is None:
        _short_term_alert_service = ShortTermPriceAlertService()
    return _short_term_alert_service
