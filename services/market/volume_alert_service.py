#!/usr/bin/env python3
"""
Volume Alert Service - Monitor volume spikes for watchlist symbols
"""

import asyncio
import logging
import threading
import time
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
from collections import defaultdict, deque

from services.core.error_service import handle_service_errors
from services.market.market_service import get_market_service
from utils.config import get_watchlist_symbols, load_config

logger = logging.getLogger(__name__)

class VolumeAlertService:
    """Service for monitoring volume spikes in watchlist symbols"""
    
    def __init__(self):
        self.market_service = get_market_service()
        self.is_running = False
        self.monitor_task: Optional[asyncio.Task] = None
        
        # Volume history for MA calculation
        self.volume_history_h4 = defaultdict(lambda: deque(maxlen=20))  # Keep last 20 H4 candles
        self.volume_history_d1 = defaultdict(lambda: deque(maxlen=20))  # Keep last 20 D1 candles

        # Alert tracking by candle timestamp
        self.alerted_candles = {}  # {symbol_timeframe_threshold: candle_timestamp}
        
        # Alert callbacks
        self.alert_callbacks = []
        
        # Load configuration
        self._load_config()
        
    def _load_config(self):
        """Load volume alert configuration"""
        config = load_config()
        volume_config = config.get('volume_alerts', {})
        
        self.monitoring_interval = volume_config.get('monitoring_interval', 300)  # 5 minutes
        self.ma_period = volume_config.get('ma_period', 20)  # MA20
        self.thresholds = volume_config.get('thresholds', [1.8, 2.2])  # Volume/MA ratios
        self.enabled = volume_config.get('enabled', True)
        
        logger.info(f"Volume alert service configured: interval={self.monitoring_interval}s, thresholds={self.thresholds}")
    
    def register_alert_callback(self, callback):
        """Register callback for volume alerts"""
        self.alert_callbacks.append(callback)
        logger.info("Volume alert callback registered")
    
    async def start_monitoring(self):
        """Start volume monitoring"""
        if self.is_running or not self.enabled:
            return
            
        self.is_running = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("🔊 Volume alert monitoring started")
    
    async def stop_monitoring(self):
        """Stop volume monitoring"""
        self.is_running = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("🔇 Volume alert monitoring stopped")
    
    async def _monitor_loop(self):
        """Main monitoring loop"""
        while self.is_running:
            try:
                await self._check_volume_alerts()
                self._cleanup_old_candle_tracking()
                await asyncio.sleep(self.monitoring_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in volume monitor loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retry
    
    @handle_service_errors
    async def _check_volume_alerts(self):
        """Check for volume alerts on watchlist symbols"""
        try:
            watchlist_symbols = get_watchlist_symbols()
            if not watchlist_symbols:
                logger.warning("No watchlist symbols configured")
                return
            
            alerts = []
            
            for symbol in watchlist_symbols:
                # Check H4 timeframe
                h4_alert = await self._check_symbol_volume(symbol, '4h')
                if h4_alert:
                    alerts.append(h4_alert)
                
                # Check D1 timeframe
                d1_alert = await self._check_symbol_volume(symbol, '1d')
                if d1_alert:
                    alerts.append(d1_alert)
                
                # Small delay between symbols to avoid rate limits
                await asyncio.sleep(0.5)
            
            # Send alerts if any
            if alerts:
                await self._send_alerts(alerts)
                
        except Exception as e:
            logger.error(f"Error checking volume alerts: {e}")
    
    async def _check_symbol_volume(self, symbol: str, timeframe: str) -> Optional[Dict[str, Any]]:
        """Check volume for a specific symbol and timeframe"""
        try:
            # Fetch OHLCV data
            df = self.market_service._fetch_ohlcv_data(symbol, timeframe, limit=21)
            if df is None or df.empty or len(df) < 2:
                return None

            # Get current candle data
            current_candle = df.iloc[-1]
            current_volume = float(current_candle['volume'])
            current_timestamp = int(current_candle['timestamp'].timestamp() * 1000)  # Convert to milliseconds

            # Get historical volumes (exclude current candle)
            volumes = [float(vol) for vol in df['volume'].iloc[:-1]]
            
            # Update volume history
            history_key = f"{symbol}_{timeframe}"
            if timeframe == '4h':
                self.volume_history_h4[history_key].extend(volumes)
                volume_history = list(self.volume_history_h4[history_key])
            else:  # 1d
                self.volume_history_d1[history_key].extend(volumes)
                volume_history = list(self.volume_history_d1[history_key])
            
            # Calculate MA20 if we have enough data
            if len(volume_history) < self.ma_period:
                return None
            
            ma20_volume = sum(volume_history[-self.ma_period:]) / self.ma_period
            
            if ma20_volume == 0:
                return None
            
            # Calculate volume ratio
            volume_ratio = current_volume / ma20_volume
            
            # Check thresholds
            for threshold in sorted(self.thresholds, reverse=True):
                if volume_ratio >= threshold:
                    # Check if we already alerted for this candle and threshold
                    alert_key = f"{symbol}_{timeframe}_{threshold}"

                    if alert_key in self.alerted_candles:
                        if self.alerted_candles[alert_key] == current_timestamp:
                            continue  # Already alerted for this candle

                    # Record alert for this candle
                    self.alerted_candles[alert_key] = current_timestamp
                    
                    # Create alert data
                    alert_data = {
                        'symbol': symbol,
                        'timeframe': timeframe,
                        'current_volume': current_volume,
                        'ma20_volume': ma20_volume,
                        'volume_ratio': volume_ratio,
                        'threshold': threshold,
                        'timestamp': time.time(),
                        'candle_timestamp': current_timestamp,
                        'price': await self.market_service.get_price(symbol)
                    }
                    
                    logger.info(f"Volume alert: {symbol} {timeframe} - {volume_ratio:.2f}x MA20 (threshold: {threshold}x) [Candle: {current_timestamp}]")
                    return alert_data
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking volume for {symbol} {timeframe}: {e}")
            return None
    
    async def _send_alerts(self, alerts: List[Dict[str, Any]]):
        """Send volume alerts to registered callbacks"""
        try:
            for callback in self.alert_callbacks:
                await callback('volume_spike', {'alerts': alerts})
        except Exception as e:
            logger.error(f"Error sending volume alerts: {e}")

    def _cleanup_old_candle_tracking(self):
        """Clean up old candle tracking data to prevent memory leak"""
        try:
            current_time = time.time()
            # Keep tracking data for last 24 hours (86400 seconds)
            cutoff_time = (current_time - 86400) * 1000  # Convert to milliseconds

            keys_to_remove = []
            for key, candle_timestamp in self.alerted_candles.items():
                if candle_timestamp < cutoff_time:
                    keys_to_remove.append(key)

            for key in keys_to_remove:
                del self.alerted_candles[key]

            if keys_to_remove:
                logger.debug(f"Cleaned up {len(keys_to_remove)} old candle tracking entries")

        except Exception as e:
            logger.error(f"Error cleaning up candle tracking: {e}")

# Global instance
_volume_alert_service = None
_instance_lock = threading.RLock()

def get_volume_alert_service() -> VolumeAlertService:
    """Get singleton instance of VolumeAlertService"""
    global _volume_alert_service
    with _instance_lock:
        if _volume_alert_service is None:
            _volume_alert_service = VolumeAlertService()
        return _volume_alert_service
