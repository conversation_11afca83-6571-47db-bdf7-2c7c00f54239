import logging
import discord

from utils.config import get_admin_id, get_guild_id

logger = logging.getLogger(__name__)

class DiscordBotLoggingService:
    def __init__(self, bot):
        self.bot = bot
        self.admin_id = get_admin_id()
        self.guild_id = get_guild_id()

    async def send_error_notification(self, error_type: str, error_message: str,
                                    function_name: str = None, severity: str = "ERROR"):
        try:
            embed = await self._create_error_embed(error_type, error_message, function_name, severity)
            await self._send_to_bot_logs(embed, f"system error ({error_type})")
        except Exception as e:
            logger.error(f"Failed to send error notification to Discord: {e}")

    async def send_status_notification(self, status_type: str, message: str, details: str = None):
        try:
            embed = await self._create_status_embed(status_type, message, details)
            await self._send_to_bot_logs(embed, f"status update ({status_type})")
        except Exception as e:
            logger.error(f"Failed to send status notification to Discord: {e}")

    async def send_warning_notification(self, warning_type: str, warning_message: str,
                                      function_name: str = None):
        try:
            embed = await self._create_warning_embed(warning_type, warning_message, function_name)
            await self._send_to_bot_logs(embed, f"system warning ({warning_type})")
        except Exception as e:
            logger.error(f"Failed to send warning notification to Discord: {e}")

    async def _send_to_bot_logs(self, embed: discord.Embed, log_description: str):
        if self.guild_id:
            try:
                guild = self.bot.get_guild(int(self.guild_id))
                if guild:
                    target_channel = None
                    for channel in guild.channels:
                        if hasattr(channel, 'name') and isinstance(channel, discord.TextChannel):
                            if any(name in channel.name.lower() for name in ['bot-logs', 'bot-log', 'botlogs']):
                                target_channel = channel
                                break

                    if not target_channel:
                        for channel in guild.channels:
                            if hasattr(channel, 'name') and isinstance(channel, discord.TextChannel):
                                if 'general' in channel.name.lower():
                                    target_channel = channel
                                    break

                    if target_channel:
                        await target_channel.send(embed=embed)
                        logger.info(f"Sent {log_description} to #{target_channel.name}")
                    else:
                        logger.warning(f"No suitable channel found for {log_description}")
            except Exception as e:
                logger.error(f"Failed to send {log_description} to guild: {e}")

    async def _create_error_embed(self, error_type: str, error_message: str,
                                function_name: str = None, severity: str = "ERROR") -> discord.Embed:
        color_map = {
            "CRITICAL": 0xff0000,
            "ERROR": 0xff6b6b,
            "WARNING": 0xffa500,
            "INFO": 0x3498db
        }
        color = color_map.get(severity, 0xff6b6b)

        embed = discord.Embed(
            title=f"🚨 {severity}: {error_type}",
            description=f"**Lỗi hệ thống được phát hiện**",
            color=color,
            timestamp=discord.utils.utcnow()
        )

        embed.add_field(
            name="📝 Chi tiết lỗi",
            value=f"```{error_message[:1000]}```",
            inline=False
        )

        if function_name:
            embed.add_field(
                name="🔧 Hàm gặp lỗi",
                value=f"`{function_name}`",
                inline=True
            )

        embed.add_field(
            name="⚠️ Mức độ",
            value=f"**{severity}**",
            inline=True
        )

        embed.set_footer(text="ChartFix Bot System Monitor")
        embed.set_thumbnail(url="https://cdn-icons-png.flaticon.com/512/564/564619.png")

        return embed

    async def _create_status_embed(self, status_type: str, message: str,
                                 details: str = None) -> discord.Embed:
        status_config = {
            "startup": {"color": 0x00ff00, "emoji": "🚀", "title": "Bot Khởi Động"},
            "shutdown": {"color": 0xff9500, "emoji": "🛑", "title": "Bot Tắt"},
            "ready": {"color": 0x00ff88, "emoji": "✅", "title": "Bot Sẵn Sàng"},
            "health_check": {"color": 0x3498db, "emoji": "🏥", "title": "Kiểm Tra Sức Khỏe"},
            "maintenance": {"color": 0xffa500, "emoji": "🔧", "title": "Bảo Trì Hệ Thống"}
        }

        config = status_config.get(status_type, {"color": 0x3498db, "emoji": "ℹ️", "title": "Trạng Thái Hệ Thống"})

        embed = discord.Embed(
            title=f"{config['emoji']} {config['title']}",
            description=message,
            color=config['color'],
            timestamp=discord.utils.utcnow()
        )

        if details:
            embed.add_field(
                name="📊 Chi tiết",
                value=details,
                inline=False
            )

        embed.set_footer(text="ChartFix Bot System Monitor")

        return embed

    async def _create_warning_embed(self, warning_type: str, warning_message: str,
                                  function_name: str = None) -> discord.Embed:
        embed = discord.Embed(
            title=f"⚠️ CẢNH BÁO: {warning_type}",
            description=f"**Hệ thống phát hiện vấn đề cần chú ý**",
            color=0xffa500,
            timestamp=discord.utils.utcnow()
        )

        embed.add_field(
            name="📝 Nội dung cảnh báo",
            value=warning_message,
            inline=False
        )

        if function_name:
            embed.add_field(
                name="🔧 Hàm liên quan",
                value=f"`{function_name}`",
                inline=True
            )

        embed.add_field(
            name="🔍 Hành động",
            value="Theo dõi và xem xét khắc phục",
            inline=True
        )

        embed.set_footer(text="ChartFix Bot System Monitor")
        embed.set_thumbnail(url="https://cdn-icons-png.flaticon.com/512/1828/1828843.png")

        return embed

_discord_bot_logging_service = None

def get_discord_bot_logging_service(bot) -> DiscordBotLoggingService:
    global _discord_bot_logging_service
    if _discord_bot_logging_service is None:
        _discord_bot_logging_service = DiscordBotLoggingService(bot)
    return _discord_bot_logging_service
