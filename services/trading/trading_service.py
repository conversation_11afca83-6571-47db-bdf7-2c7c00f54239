import logging
import ccxt
import time
from typing import Dict, Any

from utils.config import get_binance_credentials, load_config
from services.core.error_service import handle_service_errors, retry_with_backoff, NetworkError, TimeoutError
from services.core.symbol_service import format_symbol_for_exchange

def safe_float(value, default=0):
    """Safely convert value to float"""
    if value is None or value == '':
        return default
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

logger = logging.getLogger(__name__)

class BinanceFuturesTrading:
    """Binance Futures trading service - optimized for hedge mode"""

    def __init__(self):
        self.exchange = None
        self._initialize_exchange()

    def _initialize_exchange(self):
        """Initialize Binance Futures exchange"""
        try:
            credentials = get_binance_credentials()
            config = load_config()

            api_key = credentials.get('api_key', '')
            api_secret = credentials.get('api_secret', '')
            use_testnet = config.get('binance', {}).get('testnet', False)

            if not api_key or not api_secret:
                raise ValueError("Binance API credentials not found in config")

            self.exchange = ccxt.binance({
                'apiKey': api_key,
                'secret': api_secret,
                'timeout': 30000,
                'enableRateLimit': True,
                'sandbox': use_testnet,
                'options': {
                    'defaultType': 'future',
                    'warnOnFetchOpenOrdersWithoutSymbol': False
                }
            })

            self.exchange.load_markets()
            logger.info(f"Binance Futures initialized (testnet: {use_testnet})")

        except Exception as e:
            logger.error(f"Failed to initialize Binance Futures: {e}")
            raise

    @handle_service_errors
    @retry_with_backoff(max_retries=3, initial_backoff=2.0, max_backoff=10.0,
                       exception_types=[NetworkError, TimeoutError, ccxt.NetworkError, ccxt.ExchangeNotAvailable])
    def get_account_balance(self) -> Dict[str, Any]:
        """Get futures account balance with wallet balance and total balance"""
        try:
            # Get detailed futures account info
            account_info = self.exchange.fapiPrivateV3GetAccount()

            # Extract USDT balance info
            usdt_asset = None
            for asset in account_info.get('assets', []):
                if asset.get('asset') == 'USDT':
                    usdt_asset = asset
                    break

            if usdt_asset:
                wallet_balance = safe_float(usdt_asset.get('walletBalance', 0))
                margin_balance = safe_float(usdt_asset.get('marginBalance', 0))
                available_balance = safe_float(usdt_asset.get('availableBalance', 0))

                return {
                    'success': True,
                    'wallet_usdt': wallet_balance,  # Số dư ví (không bao gồm PNL)
                    'total_usdt': margin_balance,   # Số dư margin (bao gồm PNL)
                    'free_usdt': available_balance  # Số dư khả dụng
                }
            else:
                # Fallback to standard balance if detailed info not available
                balance = self.exchange.fetch_balance()
                return {
                    'success': True,
                    'wallet_usdt': balance.get('USDT', {}).get('total', 0),
                    'total_usdt': balance.get('USDT', {}).get('total', 0),
                    'free_usdt': balance.get('USDT', {}).get('free', 0)
                }

        except ccxt.DDoSProtection as e:
            logger.warning(f"Rate limit hit when fetching balance: {e}")
            time.sleep(5)
            raise NetworkError(f"Rate limit exceeded: {e}", e)
        except (ccxt.NetworkError, ccxt.ExchangeNotAvailable) as e:
            logger.error(f"Network error fetching balance: {e}")
            raise NetworkError(f"Network connection issue: {e}", e)
        except Exception as e:
            logger.error(f"Error fetching balance: {e}")
            return {'success': False, 'error': str(e)}



    @handle_service_errors
    def place_market_order(self, symbol: str, side: str, amount: float,
                          position_side: str = None) -> Dict[str, Any]:
        """
        Place market order (hedge mode optimized)

        Args:
            symbol: Trading symbol
            side: 'buy' or 'sell'
            amount: Amount to trade
            position_side: 'LONG' for buy, 'SHORT' for sell (auto-set if None)
        """
        try:
            if position_side is None:
                position_side = 'LONG' if side == 'buy' else 'SHORT'

            formatted_symbol = format_symbol_for_exchange(symbol)

            order = self.exchange.create_market_order(
                symbol=formatted_symbol,
                side=side,
                amount=amount,
                params={'positionSide': position_side}
            )

            logger.info(f"Market {side}: {amount} {symbol} (position: {position_side})")
            return {
                'success': True,
                'order_id': order.get('id'),
                'status': order.get('status')
            }

        except Exception as e:
            logger.error(f"Market order error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def place_limit_order(self, symbol: str, side: str, amount: float, price: float,
                         position_side: str = None) -> Dict[str, Any]:
        """
        Place limit order (hedge mode optimized)

        Args:
            symbol: Trading symbol
            side: 'buy' or 'sell'
            amount: Amount to trade
            price: Limit price
            position_side: 'LONG' for buy, 'SHORT' for sell (auto-set if None)
        """
        try:
            if position_side is None:
                position_side = 'LONG' if side == 'buy' else 'SHORT'

            formatted_symbol = format_symbol_for_exchange(symbol)

            order = self.exchange.create_limit_order(
                symbol=formatted_symbol,
                side=side,
                amount=amount,
                price=price,
                params={'positionSide': position_side}
            )

            logger.info(f"Limit {side}: {amount} {symbol} @ {price} (position: {position_side})")
            return {
                'success': True,
                'order_id': order.get('id'),
                'status': order.get('status')
            }

        except Exception as e:
            logger.error(f"Limit order error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def place_stop_loss_order(self, symbol: str, side: str, amount: float, stop_price: float,
                             position_side: str = None, reduce_only: bool = False) -> Dict[str, Any]:
        """
        Place stop loss order (hedge mode optimized)

        Args:
            symbol: Trading symbol
            side: 'buy' or 'sell'
            amount: Amount to trade
            stop_price: Stop price to trigger
            position_side: 'LONG' for buy, 'SHORT' for sell (auto-set if None)
            reduce_only: Whether to reduce existing position only
        """
        try:
            if position_side is None:
                position_side = 'LONG' if side == 'buy' else 'SHORT'

            formatted_symbol = format_symbol_for_exchange(symbol)

            params = {
                'stopPrice': stop_price,
                'positionSide': position_side
            }
            if reduce_only:
                params['reduceOnly'] = True

            order = self.exchange.create_order(
                symbol=formatted_symbol,
                type='STOP_MARKET',
                side=side,
                amount=amount,
                params=params
            )

            logger.info(f"Stop loss: {side} {amount} {symbol} @ {stop_price} (position: {position_side})")
            return {
                'success': True,
                'order_id': order.get('id'),
                'status': order.get('status')
            }

        except Exception as e:
            logger.error(f"Stop loss error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def place_take_profit_order(self, symbol: str, side: str, amount: float, stop_price: float,
                               position_side: str = None, reduce_only: bool = False) -> Dict[str, Any]:
        """
        Place take profit order (hedge mode optimized)

        Args:
            symbol: Trading symbol
            side: 'buy' or 'sell'
            amount: Amount to trade
            stop_price: Price to trigger take profit
            position_side: 'LONG' for buy, 'SHORT' for sell (auto-set if None)
            reduce_only: Whether to reduce existing position only
        """
        try:
            if position_side is None:
                position_side = 'LONG' if side == 'buy' else 'SHORT'

            formatted_symbol = format_symbol_for_exchange(symbol)

            params = {
                'stopPrice': stop_price,
                'positionSide': position_side
            }
            if reduce_only:
                params['reduceOnly'] = True

            order = self.exchange.create_order(
                symbol=formatted_symbol,
                type='TAKE_PROFIT_MARKET',
                side=side,
                amount=amount,
                params=params
            )

            logger.info(f"Take profit: {side} {amount} {symbol} @ {stop_price} (position: {position_side})")
            return {
                'success': True,
                'order_id': order.get('id'),
                'status': order.get('status')
            }

        except Exception as e:
            logger.error(f"Take profit error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def cancel_order(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """Cancel order"""
        try:
            formatted_symbol = format_symbol_for_exchange(symbol)
            self.exchange.cancel_order(order_id, formatted_symbol)

            logger.info(f"Cancelled order: {order_id}")
            return {'success': True}

        except Exception as e:
            logger.error(f"Cancel order error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def cancel_orders_by_symbol(self, symbol: str, side: str = None) -> Dict[str, Any]:
        """Cancel all orders for a symbol and optionally by side"""
        try:
            formatted_symbol = format_symbol_for_exchange(symbol)

            # Get all open orders for the symbol
            orders = self.exchange.fetch_open_orders(formatted_symbol)

            if not orders:
                return {'success': True, 'cancelled_count': 0, 'message': 'No orders to cancel'}

            cancelled_orders = []
            failed_orders = []

            for order in orders:
                # Filter by side if specified
                if side and order.get('side', '').upper() != side.upper():
                    continue

                try:
                    self.exchange.cancel_order(order['id'], formatted_symbol)
                    cancelled_orders.append(order['id'])
                    logger.info(f"Cancelled order: {order['id']}")
                except Exception as e:
                    failed_orders.append({'id': order['id'], 'error': str(e)})
                    logger.error(f"Failed to cancel order {order['id']}: {e}")

            return {
                'success': True,
                'cancelled_count': len(cancelled_orders),
                'failed_count': len(failed_orders),
                'cancelled_orders': cancelled_orders,
                'failed_orders': failed_orders
            }

        except Exception as e:
            logger.error(f"Cancel orders by symbol error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def get_open_orders(self, symbol: str = None) -> Dict[str, Any]:
        """Get open orders"""
        try:
            formatted_symbol = format_symbol_for_exchange(symbol) if symbol else None
            orders = self.exchange.fetch_open_orders(formatted_symbol)

            return {
                'success': True,
                'orders': orders,
                'count': len(orders)
            }

        except Exception as e:
            logger.error(f"Get orders error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def get_order_status(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """Get order status"""
        try:
            formatted_symbol = format_symbol_for_exchange(symbol)
            order = self.exchange.fetch_order(order_id, formatted_symbol)

            return {
                'success': True,
                'status': order.get('status'),
                'filled': order.get('filled', 0)
            }

        except Exception as e:
            logger.error(f"Get order status error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def get_positions(self) -> Dict[str, Any]:
        """Get all positions"""
        try:
            positions = self.exchange.fetch_positions()
            return {
                'success': True,
                'positions': positions
            }
        except Exception as e:
            logger.error(f"Get positions error: {e}")
            return {'success': False, 'error': str(e), 'positions': []}

    @handle_service_errors
    def close_position(self, symbol: str, side: str = None, percentage: float = 100.0) -> Dict[str, Any]:
        """Close position"""
        try:
            formatted_symbol = format_symbol_for_exchange(symbol)

            # Get current position
            positions = self.exchange.fetch_positions([formatted_symbol])
            target_position = None

            for pos in positions:
                if pos['symbol'] == formatted_symbol:
                    contracts = safe_float(pos.get('contracts'))
                    if contracts == 0:
                        continue

                    # Determine position side based on contracts sign
                    position_side = 'LONG' if contracts > 0 else 'SHORT'

                    if side is None or position_side == side.upper():
                        target_position = pos
                        break

            if not target_position:
                return {'success': False, 'error': 'No matching position found'}

            # Calculate amount to close
            current_amount = abs(safe_float(target_position['contracts']))
            close_amount = current_amount * (percentage / 100.0)

            # Determine close side (opposite of position)
            contracts = safe_float(target_position['contracts'])
            close_side = 'sell' if contracts > 0 else 'buy'

            # Place market order to close
            order = self.exchange.create_market_order(
                symbol=formatted_symbol,
                side=close_side,
                amount=close_amount,
                params={'reduceOnly': True}
            )

            return {
                'success': True,
                'order_id': order.get('id'),
                'status': order.get('status')
            }

        except Exception as e:
            logger.error(f"Close position error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def set_take_profit(self, symbol: str, side: str, price: float = None, percentage: float = None) -> Dict[str, Any]:
        """Set take profit order"""
        try:
            formatted_symbol = format_symbol_for_exchange(symbol)

            # Get current position to determine amount
            positions = self.exchange.fetch_positions([formatted_symbol])
            target_position = None

            for pos in positions:
                if pos['symbol'] == formatted_symbol:
                    contracts = float(pos.get('contracts', 0))
                    if contracts == 0:
                        continue

                    # Determine position side based on contracts sign
                    position_side = 'LONG' if contracts > 0 else 'SHORT'

                    if position_side == side.upper():
                        target_position = pos
                        break

            if not target_position:
                return {'success': False, 'error': 'No matching position found'}

            current_amount = abs(safe_float(target_position['contracts']))
            tp_amount = current_amount * (percentage / 100.0) if percentage else current_amount

            # Determine TP side (opposite of position)
            tp_side = 'sell' if side.upper() == 'LONG' else 'buy'

            # If no price specified, calculate based on current position
            if not price:
                entry_price = safe_float(target_position['entryPrice'])
                if side.upper() == 'LONG':
                    price = entry_price * 1.02  # 2% profit
                else:
                    price = entry_price * 0.98  # 2% profit

            # Place take profit order
            order = self.exchange.create_limit_order(
                symbol=formatted_symbol,
                side=tp_side,
                amount=tp_amount,
                price=price,
                params={'reduceOnly': True}
            )

            return {
                'success': True,
                'order_id': order.get('id'),
                'status': order.get('status')
            }

        except Exception as e:
            logger.error(f"Set take profit error: {e}")
            return {'success': False, 'error': str(e)}

    @handle_service_errors
    def set_stop_loss(self, symbol: str, side: str, price: float = None, percentage: float = None) -> Dict[str, Any]:
        """Set stop loss order"""
        try:
            formatted_symbol = format_symbol_for_exchange(symbol)

            # Get current position to determine amount
            positions = self.exchange.fetch_positions([formatted_symbol])
            target_position = None

            for pos in positions:
                if pos['symbol'] == formatted_symbol:
                    contracts = float(pos.get('contracts', 0))
                    if contracts == 0:
                        continue

                    # Determine position side based on contracts sign
                    position_side = 'LONG' if contracts > 0 else 'SHORT'

                    if position_side == side.upper():
                        target_position = pos
                        break

            if not target_position:
                return {'success': False, 'error': 'No matching position found'}

            current_amount = abs(safe_float(target_position['contracts']))
            sl_amount = current_amount * (percentage / 100.0) if percentage else current_amount

            # Determine SL side (opposite of position)
            sl_side = 'sell' if side.upper() == 'LONG' else 'buy'

            # If no price specified, calculate based on current position
            if not price:
                entry_price = safe_float(target_position['entryPrice'])
                if side.upper() == 'LONG':
                    price = entry_price * 0.98  # 2% loss
                else:
                    price = entry_price * 1.02  # 2% loss

            # Place stop loss order
            order = self.exchange.create_order(
                symbol=formatted_symbol,
                type='stop_market',
                side=sl_side,
                amount=sl_amount,
                price=None,
                params={
                    'stopPrice': price,
                    'reduceOnly': True
                }
            )

            return {
                'success': True,
                'order_id': order.get('id'),
                'status': order.get('status')
            }

        except Exception as e:
            logger.error(f"Set stop loss error: {e}")
            return {'success': False, 'error': str(e)}
