import logging
import asyncio
import discord
from discord.ext import commands
from discord import app_commands
from typing import Dict, Any

from services.market.market_service import get_market_service
from services.market.market_monitor_service import MarketMonitorService
from services.market.chart_service import get_chart_service

from services.core.symbol_service import smart_normalize_symbol
from utils.ui_components import (
    format_price_display,
    format_price_enhanced,
    format_volume_enhanced,
    format_percentage_enhanced,
    create_mobile_optimized_table
)
from utils.constants import EMOJI, DISCORD_FORMATTING, MESSAGE_TEMPLATES

logger = logging.getLogger(__name__)

class MarketCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.market_service = get_market_service()
        self.monitor_service = MarketMonitorService()
        self.chart_service = get_chart_service()


    @app_commands.command(name="market", description="Display comprehensive cryptocurrency market overview")
    async def market(self, interaction: discord.Interaction):
        try:
            await interaction.response.defer()
        except discord.errors.NotFound:
            logger.warning(f"Interaction already expired for market command by {interaction.user}")
            return

        try:
            market_overview = await asyncio.wait_for(
                self.market_service.fetch_market_overview(),
                timeout=10.0
            )

            if not market_overview:
                await interaction.followup.send("❌ Không thể lấy dữ liệu thị trường từ CoinGecko")
                return

            embed = await self.create_market_embed(market_overview)

            await interaction.followup.send(embed=embed)

            logger.info(f"Market command executed by {interaction.user} ({interaction.user.id})")

        except asyncio.TimeoutError:
            logger.error(f"Market command timeout for {interaction.user}")
            await interaction.followup.send("❌ Timeout khi lấy dữ liệu thị trường. Vui lòng thử lại.")
        except Exception as e:
            logger.error(f"Error in market command: {e}")
            await interaction.followup.send("❌ Đã xảy ra lỗi khi lấy dữ liệu thị trường.")

    @app_commands.command(name="spread", description="Compare spot vs futures prices and show spread")
    @app_commands.describe(symbol="Trading symbol (default: BTC)")
    async def spread(self, interaction: discord.Interaction, symbol: str = "BTC"):
        try:
            await interaction.response.defer()
        except discord.errors.NotFound:
            logger.warning(f"Interaction already expired for spread command by {interaction.user}")
            return

        try:
            if not self.bot.check_cooldown(interaction.user.id, "spread", 5):
                remaining = self.bot.get_cooldown_remaining(interaction.user.id, "spread", 5)
                await interaction.followup.send(f"⏰ Vui lòng đợi {remaining:.1f}s trước khi xem spread tiếp theo.", ephemeral=True)
                return

            # Normalize symbol
            from services.core.symbol_service import smart_normalize_symbol
            normalized_symbol = smart_normalize_symbol(symbol)

            # Get spot vs futures data
            spread_data = await asyncio.wait_for(
                self._fetch_spot_futures_data(normalized_symbol),
                timeout=10.0
            )

            if not spread_data:
                await interaction.followup.send(f"❌ Không thể lấy dữ liệu spread cho {symbol}. Vui lòng kiểm tra symbol và thử lại.")
                return

            embed = await self._create_spread_embed(spread_data, symbol)
            await interaction.followup.send(embed=embed)

            logger.info(f"Spread command executed by {interaction.user} ({interaction.user.id}) for {symbol}")

        except asyncio.TimeoutError:
            logger.error(f"Spread command timeout for {interaction.user}")
            await interaction.followup.send("❌ Timeout khi lấy dữ liệu spread. Vui lòng thử lại.")
        except Exception as e:
            logger.error(f"Error in spread command: {e}")
            await interaction.followup.send("❌ Đã xảy ra lỗi khi lấy dữ liệu spread.")

    @app_commands.command(name="rates", description="Display Binance Earn rates and P2P USDT/VND prices")
    async def rates(self, interaction: discord.Interaction):
        try:
            await interaction.response.defer()
        except discord.errors.NotFound:
            logger.warning(f"Interaction already expired for rates command by {interaction.user}")
            return

        try:
            rates_data = await asyncio.wait_for(
                self.monitor_service.get_current_rates(),
                timeout=8.0
            )

            embed = await self.create_rates_embed(rates_data)

            await interaction.followup.send(embed=embed)

            logger.info(f"Rates command executed by {interaction.user} ({interaction.user.id})")

        except asyncio.TimeoutError:
            logger.error(f"Rates command timeout for {interaction.user}")
            await interaction.followup.send("❌ Timeout khi lấy dữ liệu lãi suất. Vui lòng thử lại.")
        except Exception as e:
            logger.error(f"Error in rates command: {e}")
            await interaction.followup.send("❌ Đã xảy ra lỗi khi lấy dữ liệu lãi suất.")

    @app_commands.command(name="c", description="Generate financial chart for cryptocurrency")
    @app_commands.describe(
        symbol="Trading symbol (e.g., BTCUSDT, ETHUSDT)",
        timeframe="Time interval (1m, 5m, 15m, 1h, 4h, 1d, 1w)",
        chart_type="Chart type (candlestick or line)",
        indicators="Include indicators (ema, volume)"
    )
    async def chart(self, interaction: discord.Interaction,
                   symbol: str,
                   timeframe: str,
                   chart_type: str = "candlestick",
                   indicators: str = "ema,volume"):
        try:
            await interaction.response.defer()
        except discord.errors.NotFound:
            logger.warning(f"Interaction already expired for chart command by {interaction.user}")
            return

        try:
            if not self.bot.check_cooldown(interaction.user.id, "chart", 10):
                remaining = self.bot.get_cooldown_remaining(interaction.user.id, "chart", 10)
                await interaction.followup.send(f"⏰ Vui lòng đợi {remaining:.1f}s trước khi tạo chart tiếp theo.", ephemeral=True)
                return

            symbol = smart_normalize_symbol(symbol)
            timeframe = timeframe.lower().strip()
            chart_type = chart_type.lower().strip()

            if not self.chart_service.is_valid_timeframe(timeframe):
                supported = ", ".join(self.chart_service.get_supported_timeframes())
                await interaction.followup.send(f"❌ Timeframe không hợp lệ. Hỗ trợ: {supported}")
                return

            if chart_type not in ["candlestick", "line"]:
                await interaction.followup.send("❌ Chart type phải là 'candlestick' hoặc 'line'")
                return

            indicator_list = [ind.strip() for ind in indicators.split(",") if ind.strip()]
            valid_indicators = ["ema", "volume"]
            indicator_list = [ind for ind in indicator_list if ind in valid_indicators]

            if not indicator_list:
                indicator_list = ["ema", "volume"]

            chart_buffer = await asyncio.wait_for(
                self.chart_service.generate_chart(symbol, timeframe, chart_type, indicator_list),
                timeout=30.0
            )

            if chart_buffer is None:
                await interaction.followup.send(f"❌ Không thể tạo chart cho {symbol} {timeframe}. Vui lòng kiểm tra symbol và thử lại.")
                return

            chart_buffer.seek(0)
            file = discord.File(chart_buffer, filename=f"{symbol}_{timeframe}_chart.png")

            title = MESSAGE_TEMPLATES['chart_command']['title_format'].format(
                emoji=EMOJI['chart'],
                symbol=symbol
            )

            description = MESSAGE_TEMPLATES['chart_command']['description_format'].format(
                timeframe=timeframe,
                chart_type=chart_type,
                indicators=', '.join(indicator_list)
            )

            embed = discord.Embed(
                title=title,
                description=description,
                color=DISCORD_FORMATTING['colors']['chart'],
                timestamp=discord.utils.utcnow()
            )
            embed.set_image(url=f"attachment://{symbol}_{timeframe}_chart.png")
            embed.set_footer(text=DISCORD_FORMATTING['footers']['chart_data'])

            await interaction.followup.send(embed=embed, file=file)
            logger.info(f"Chart command executed by {interaction.user} ({interaction.user.id}) for {symbol} {timeframe}")

        except asyncio.TimeoutError:
            logger.error(f"Chart command timeout for {interaction.user}")
            await interaction.followup.send("❌ Timeout khi tạo chart. Vui lòng thử lại.")
        except Exception as e:
            logger.error(f"Error in chart command: {e}")
            await interaction.followup.send("❌ Đã xảy ra lỗi khi tạo chart.")

    @app_commands.command(name="p", description="Display detailed price information for cryptocurrency")
    @app_commands.describe(
        symbol="Cryptocurrency symbol (e.g., btc, eth, btcusdc, ethbtc)"
    )
    async def p(self, interaction: discord.Interaction, symbol: str):
        """Display comprehensive price information for a cryptocurrency"""

        try:
            await interaction.response.defer()
        except discord.errors.NotFound:
            logger.warning(f"Interaction already expired for price command by {interaction.user}")
            return

        try:
            if not self.bot.check_cooldown(interaction.user.id, "p", 5):
                remaining = self.bot.get_cooldown_remaining(interaction.user.id, "p", 5)
                await interaction.followup.send(f"⏰ Vui lòng đợi {remaining:.1f}s trước khi xem giá tiếp theo.", ephemeral=True)
                return

            # Use smart symbol normalization
            normalized_symbol = smart_normalize_symbol(symbol)

            # Get comprehensive price data
            price_data = await asyncio.wait_for(
                self.market_service.get_comprehensive_price_data(normalized_symbol),
                timeout=15.0
            )

            if not price_data.get('success', False):
                await interaction.followup.send(f"❌ Không thể lấy dữ liệu giá cho {symbol}. Vui lòng kiểm tra symbol và thử lại.")
                return

            # Create price embed
            embed = await self.create_price_embed(price_data, symbol)

            await interaction.followup.send(embed=embed)
            logger.info(f"P command executed by {interaction.user} ({interaction.user.id}) for {symbol} -> {normalized_symbol}")

        except asyncio.TimeoutError:
            logger.error(f"P command timeout for {interaction.user}")
            await interaction.followup.send("❌ Timeout khi lấy dữ liệu giá. Vui lòng thử lại.")
        except Exception as e:
            logger.error(f"Error in p command: {e}")
            await interaction.followup.send("❌ Đã xảy ra lỗi khi lấy dữ liệu giá.")



    async def create_price_embed(self, price_data: dict, original_symbol: str) -> discord.Embed:
        """Create Discord embed for price information"""
        try:
            symbol = price_data.get('symbol', '').replace('USDT', '')
            name = price_data.get('name', symbol)
            current_price = price_data.get('current_price', 0)
            high_24h = price_data.get('high_24h', 0)
            low_24h = price_data.get('low_24h', 0)
            volume_24h = price_data.get('volume_24h', 0)
            market_cap = price_data.get('market_cap', 0)

            # Percentage changes
            change_1h = price_data.get('price_change_1h', 0)
            change_24h = price_data.get('price_change_24h', 0)
            change_7d = price_data.get('price_change_7d', 0)
            change_30d = price_data.get('price_change_30d', 0)

            # ATH data
            ath = price_data.get('ath', 0)
            ath_change = price_data.get('ath_change_percentage', 0)

            # Use enhanced formatting functions for consistent cross-platform display
            price_str = format_price_enhanced(current_price)
            high_str = format_price_enhanced(high_24h)
            low_str = format_price_enhanced(low_24h)
            ath_str = format_price_enhanced(ath)
            volume_str = format_volume_enhanced(volume_24h)
            mcap_str = format_volume_enhanced(market_cap) if market_cap > 0 else "N/A"

            # Create mobile-optimized data structure using enhanced formatting
            price_data_formatted = {
                f"{name} - ${symbol}": "",
                f"{EMOJI['price']} Price": price_str,
                f"{EMOJI['balance']} H/L": f"{high_str} | {low_str}",
                f"{EMOJI['chart']} 1h": format_percentage_enhanced(change_1h),
                f"{EMOJI['chart']} 24h": format_percentage_enhanced(change_24h),
                f"{EMOJI['rocket']} 7d": format_percentage_enhanced(change_7d),
                f"{EMOJI['moon']} 30d": format_percentage_enhanced(change_30d),
                f"{EMOJI['trophy']} ATH": f"{ath_str} ({format_percentage_enhanced(ath_change)})",
                f"{EMOJI['volume']} 24h Vol": volume_str,
                f"{EMOJI['diamond']} MCap": mcap_str
            }

            # Create mobile-optimized table
            description_content = create_mobile_optimized_table(price_data_formatted)

            # Determine embed color based on 24h change using constants
            if change_24h > 0:
                color = DISCORD_FORMATTING['colors']['profit']
            elif change_24h < 0:
                color = DISCORD_FORMATTING['colors']['loss']
            else:
                color = DISCORD_FORMATTING['colors']['neutral']

            # Use template for consistent formatting
            title = MESSAGE_TEMPLATES['price_command']['title_format'].format(
                emoji=EMOJI['price'],
                name=name,
                symbol=symbol
            )

            embed = discord.Embed(
                title=title,
                description=f"```\n{description_content}\n```",
                color=color,
                timestamp=discord.utils.utcnow()
            )

            embed.set_footer(text=DISCORD_FORMATTING['footers']['market_data'])

            return embed

        except Exception as e:
            logger.error(f"Error creating price embed: {e}")
            # Fallback embed using enhanced formatting
            return discord.Embed(
                title=f"{EMOJI['error']} Error",
                description="Không thể tạo thông tin giá",
                color=DISCORD_FORMATTING['colors']['error']
            )

    async def create_market_embed(self, market_overview) -> discord.Embed:
        """Create an enhanced Discord embed for market data"""

        # Create embed
        embed = discord.Embed(
            title="📊 CRYPTO MARKET OVERVIEW 🌍",
            color=0x00ff88,
            timestamp=discord.utils.utcnow()
        )

        market_data = market_overview.get('market_data', {})
        fear_greed = market_overview.get('fear_greed', {})

        # Market overview
        description = ""

        # Global market stats
        global_data = market_data.get('global', {})
        if global_data:
            total_cap = global_data.get('total_market_cap', 0)
            total_volume = global_data.get('total_volume_24h', 0)
            cap_change = global_data.get('market_cap_change_24h', 0)
            dominance = global_data.get('dominance', {})

            description += "```\n"
            description += "TỔNG QUAN THỊ TRƯỜNG\n"
            description += "══════════════════════════════\n"
            description += f"💰 Tổng vốn hóa:    ${total_cap/1e12:.2f}T\n"
            description += f"📊 Khối lượng 24h:  ${total_volume/1e9:.1f}B\n"

            cap_emoji = "🟢" if cap_change >= 0 else "🔴"
            cap_sign = "+" if cap_change >= 0 else ""
            description += f"{cap_emoji} Thay đổi 24h:   {cap_sign}{cap_change:.2f}%\n"

            # Ensure dominance values are properly formatted
            btc_dominance = dominance.get('btc', dominance.get('BTC', 0))
            eth_dominance = dominance.get('eth', dominance.get('ETH', 0))
            description += f"₿ BTC Dominance:    {btc_dominance:.1f}%\n"
            description += f"⟠ ETH Dominance:    {eth_dominance:.1f}%\n"
            description += "```\n"

            # Define stablecoins and USDT pairs to filter out from top volume
            excluded_tokens = {
                'USDT', 'USDC', 'BUSD', 'DAI', 'TUSD', 'USDD', 'FDUSD',
                'FRAX', 'LUSD', 'USDP', 'GUSD', 'HUSD', 'SUSD',
                'USDN', 'RSV', 'DUSD', 'OUSD', 'USTC', 'USDK',
                'BSC-USD', 'PYUSD', 'USDJ', 'USDE', 'USDB'
            }

            # Add top 10 trading volume section (excluding stablecoins and USDT pairs)
            top_volume_coins = sorted(
                [coin for coin in market_data.get('top_coins', [])
                 if coin.get('total_volume', 0) > 0
                 and coin.get('symbol', '').upper() not in excluded_tokens],
                key=lambda x: x.get('total_volume', 0),
                reverse=True
            )[:10]  # Top 10 by volume

            if top_volume_coins:
                description += "\n📊 **TOP 10 KHỐI LƯỢNG GIAO DỊCH (24H)**\n"
                description += "```\n"
                description += f"{'#':<2} {'TOKEN':<6} {'VOL':<12} {'GIÁ':<18} {'24H %'}\n"
                description += "-" * 45 + "\n"

                for i, coin in enumerate(top_volume_coins, 1):
                    symbol = coin.get('symbol', '').upper()
                    volume = coin.get('total_volume', 0)
                    price = coin.get('current_price', 0)
                    # Use unified percentage calculation service
                    from services.market.percentage_calculation_service import get_percentage_service
                    percentage_service = get_percentage_service()
                    percentage_result = percentage_service.extract_coingecko_percentage(coin)
                    change_24h = percentage_result.value if percentage_result.is_valid else 0

                    # Format volume
                    if volume >= 1_000_000_000:
                        vol_str = f"${volume/1_000_000_000:,.1f}B"
                    else:
                        vol_str = f"${volume/1_000_000:,.1f}M"

                    # Format 24h change with color
                    if change_24h > 0:
                        change_str = f"🟢+{change_24h:.1f}%"
                    elif change_24h < 0:
                        change_str = f"🔴{change_24h:.1f}%"
                    else:
                        change_str = f"⚪{change_24h:.1f}%"

                    # Format price using the new formatting function
                    price_str = format_price_display(price)

                    # Ensure consistent column widths
                    rank_str = f"{i:>2}"
                    symbol_pad = ' ' * (6 - len(symbol))
                    vol_pad = ' ' * (12 - len(vol_str))
                    price_pad = ' ' * (18 - len(price_str))

                    # Ensure price string doesn't exceed column width
                    if len(price_str) > 18:
                        price_str = price_str[:15] + "..."

                    description += f"{rank_str} {symbol}{symbol_pad} {vol_str}{vol_pad} {price_str:<18} {change_str}\n"
                description += "```\n"

        # Fear & Greed Index
        if fear_greed:
            fg_value = fear_greed.get('value', 0)
            fg_class = fear_greed.get('classification', 'Unknown')

            # Emoji based on fear/greed level
            if fg_value >= 75:
                fg_emoji = "🔥"  # Extreme Greed
            elif fg_value >= 55:
                fg_emoji = "😊"  # Greed
            elif fg_value >= 45:
                fg_emoji = "😐"  # Neutral
            elif fg_value >= 25:
                fg_emoji = "😰"  # Fear
            else:
                fg_emoji = "😱"  # Extreme Fear

            description += f"🧠 **Fear & Greed Index**: {fg_emoji} {fg_value}/100 ({fg_class})\n\n"

        embed.description = description

        # Top gainers and losers
        top_coins = market_data.get('top_coins', [])
        if top_coins:
            # Filter and sort for gainers/losers
            gainers = [coin for coin in top_coins if coin.get('price_change_percentage_24h', 0) > 0]
            losers = [coin for coin in top_coins if coin.get('price_change_percentage_24h', 0) < 0]

            gainers = sorted(gainers, key=lambda x: x.get('price_change_percentage_24h', 0), reverse=True)[:8]
            losers = sorted(losers, key=lambda x: x.get('price_change_percentage_24h', 0))[:8]

            # Top gainers
            if gainers:
                gainers_text = "```\n"
                for coin in gainers:
                    symbol = coin.get('symbol', '').upper()
                    change = coin.get('price_change_percentage_24h', 0)
                    price = coin.get('current_price', 0)

                    # Use the new price formatting function
                    price_str = format_price_display(price)

                    symbol_pad = ' ' * (8 - len(symbol))
                    price_pad = ' ' * (15 - len(price_str))

                    # Ensure price string doesn't exceed column width
                    if len(price_str) > 15:
                        price_str = price_str[:12] + "..."

                    gainers_text += f"{symbol}{symbol_pad}{price_str}{price_pad}🟢+{change:.1f}%\n"
                gainers_text += "```"
                embed.add_field(name="📈 TOP GAINERS", value=gainers_text, inline=True)

            # Top losers
            if losers:
                losers_text = "```\n"
                for coin in losers:
                    symbol = coin.get('symbol', '').upper()
                    change = coin.get('price_change_percentage_24h', 0)
                    price = coin.get('current_price', 0)

                    # Use the new price formatting function
                    price_str = format_price_display(price)

                    symbol_pad = ' ' * (8 - len(symbol))
                    price_pad = ' ' * (15 - len(price_str))

                    # Ensure price string doesn't exceed column width
                    if len(price_str) > 15:
                        price_str = price_str[:12] + "..."

                    losers_text += f"{symbol}{symbol_pad}{price_str}{price_pad}🔴{change:.1f}%\n"
                losers_text += "```"
                embed.add_field(name="📉 TOP LOSERS", value=losers_text, inline=True)

        embed.set_footer(text="Dữ liệu từ CoinGecko • Cập nhật mỗi 15 phút")
        embed.set_thumbnail(url="https://cryptologos.cc/logos/bitcoin-btc-logo.png")
        return embed

    async def create_rates_embed(self, rates_data) -> discord.Embed:
        """Create an enhanced Discord embed for rates data"""

        # Create embed
        embed = discord.Embed(
            title="💰 BINANCE RATES & P2P 💱",
            color=0xf0b90b,  # Binance yellow
            timestamp=discord.utils.utcnow()
        )

        description = ""

        # Binance Earn rates
        earn_data = rates_data.get('earn', {})
        if earn_data:
            flexible = earn_data.get('flexible', {})
            rate = flexible.get('rate', 0)

            # Format rate properly - if rate is already in percentage format
            if rate > 1:
                # Rate is in percentage format (e.g., 3.5 for 3.5%)
                rate_display = f"{rate:.2f}% APY"
            else:
                # Rate is in decimal format (e.g., 0.035 for 3.5%)
                rate_display = f"{rate*100:.2f}% APY"

            description += "```\n"
            description += "BINANCE EARN - USDT FLEXIBLE\n"
            description += "══════════════════════════════\n"
            description += f"💎 Lãi suất hiện tại: {rate_display}\n"
            description += "```\n"

        # P2P rates
        p2p_data = rates_data.get('p2p', {})
        if p2p_data:
            buy_rate = p2p_data.get('buy_rate', 0)
            sell_rate = p2p_data.get('sell_rate', 0)
            spread = p2p_data.get('spread', 0)

            description += "```\n"
            description += "BINANCE P2P - USDT/VND\n"
            description += "══════════════════════════════\n"
            description += f"💵 Giá mua USDT:     {buy_rate:>10,.0f} VND\n"
            description += f"💸 Giá bán USDT:     {sell_rate:>10,.0f} VND\n"
            description += f"📊 Chênh lệch:       {spread:>10,.0f} VND\n"
            description += f"📈 Spread:           {(spread/buy_rate*100):>9.2f}%\n"
            description += "```\n"

        embed.description = description
        embed.set_footer(text="Dữ liệu từ Binance API • Cập nhật theo thời gian thực")
        embed.set_thumbnail(url="https://cryptologos.cc/logos/binance-coin-bnb-logo.png")
        return embed



    async def _fetch_spot_futures_data(self, symbol: str) -> Dict[str, Any]:
        """Fetch spot and futures data for comparison"""
        try:
            from services.market.market_service import get_binance_spot_exchange, get_binance_futures_exchange

            # Get exchanges
            spot_exchange = get_binance_spot_exchange()
            futures_exchange = get_binance_futures_exchange()

            # Normalize symbol to get base symbol (BTC from BTCUSDT)
            if symbol.endswith('USDT'):
                base_symbol = symbol.replace('USDT', '')
            else:
                base_symbol = symbol.upper()

            # Format symbols correctly
            spot_symbol = f"{base_symbol}/USDT"
            futures_symbol = f"{base_symbol}/USDT:USDT"

            logger.info(f"Fetching spread data - Spot: {spot_symbol}, Futures: {futures_symbol}")

            # Fetch data concurrently
            loop = asyncio.get_event_loop()
            spot_ticker, futures_ticker = await asyncio.gather(
                loop.run_in_executor(None, spot_exchange.fetch_ticker, spot_symbol),
                loop.run_in_executor(None, futures_exchange.fetch_ticker, futures_symbol)
            )

            # Extract prices
            spot_price = float(spot_ticker['last'])
            futures_price = float(futures_ticker['last'])

            # Calculate differences
            price_diff = futures_price - spot_price
            percentage_diff = (price_diff / spot_price) * 100

            return {
                'symbol': base_symbol,
                'spot_price': spot_price,
                'futures_price': futures_price,
                'price_diff': price_diff,
                'percentage_diff': percentage_diff,
                'spot_volume': spot_ticker.get('quoteVolume', 0),
                'futures_volume': futures_ticker.get('quoteVolume', 0),
                'spot_change_24h': spot_ticker.get('percentage', 0),
                'futures_change_24h': futures_ticker.get('percentage', 0),
                'spot_ticker': spot_ticker,
                'futures_ticker': futures_ticker
            }

        except Exception as e:
            logger.error(f"Error fetching spot/futures data for {symbol}: {e}")
            return None

    async def _create_spread_embed(self, data: Dict[str, Any], original_symbol: str) -> discord.Embed:
        """Create embed for spot vs futures comparison"""
        try:
            symbol = data['symbol']
            spot_price = data['spot_price']
            futures_price = data['futures_price']
            price_diff = data['price_diff']
            percentage_diff = data['percentage_diff']

            # Determine color based on spread
            if percentage_diff > 0:
                color = 0x00ff88  # Green - futures premium
                spread_emoji = "🟢"
                spread_text = "FUTURES PREMIUM"
            elif percentage_diff < 0:
                color = 0xff4444  # Red - futures discount
                spread_emoji = "🔴"
                spread_text = "FUTURES DISCOUNT"
            else:
                color = 0xffaa00  # Yellow - no spread
                spread_emoji = "🟡"
                spread_text = "NO SPREAD"

            # Format prices
            spot_price_str = format_price_display(spot_price)
            futures_price_str = format_price_display(futures_price)
            price_diff_str = f"${abs(price_diff):,.2f}"

            # Format volumes
            spot_volume_str = format_volume_enhanced(data['spot_volume'])
            futures_volume_str = format_volume_enhanced(data['futures_volume'])

            # Create embed
            embed = discord.Embed(
                title=f"📊 {symbol} SPOT vs FUTURES",
                color=color,
                timestamp=discord.utils.utcnow()
            )

            # Main comparison
            description = "```\n"
            description += f"💰 SPOT PRICE      {spot_price_str:>12}\n"
            description += f"🚀 FUTURES PRICE   {futures_price_str:>12}\n"
            description += f"{'─' * 32}\n"
            description += f"{spread_emoji} SPREAD         {price_diff_str:>12}\n"
            description += f"📈 PERCENTAGE      {percentage_diff:>+9.4f}%\n"
            description += "```\n"

            # Additional info
            description += f"**{spread_text}** - "
            if abs(percentage_diff) < 0.01:
                description += "Giá gần như bằng nhau"
            elif percentage_diff > 0:
                description += f"Futures cao hơn Spot {abs(percentage_diff):.4f}%"
            else:
                description += f"Futures thấp hơn Spot {abs(percentage_diff):.4f}%"

            embed.description = description

            # Add fields
            embed.add_field(
                name="📊 24H CHANGES",
                value=f"Spot: {data['spot_change_24h']:+.2f}%\nFutures: {data['futures_change_24h']:+.2f}%",
                inline=True
            )

            embed.add_field(
                name="📈 24H VOLUMES",
                value=f"Spot: {spot_volume_str}\nFutures: {futures_volume_str}",
                inline=True
            )

            # Arbitrage opportunity
            if abs(percentage_diff) > 0.05:  # > 0.05%
                arb_text = "⚠️ **ARBITRAGE OPPORTUNITY**"
                if percentage_diff > 0:
                    arb_text += "\nBuy Spot → Sell Futures"
                else:
                    arb_text += "\nBuy Futures → Sell Spot"
                embed.add_field(name="🎯 TRADING SIGNAL", value=arb_text, inline=False)

            embed.set_footer(text="Dữ liệu từ Binance Spot & Futures")

            return embed

        except Exception as e:
            logger.error(f"Error creating spread embed: {e}")
            # Fallback embed
            return discord.Embed(
                title="❌ Error",
                description="Không thể tạo embed spread",
                color=0xff0000
            )

async def setup(bot):
    """Setup function called by Discord.py"""
    await bot.add_cog(MarketCommands(bot))
