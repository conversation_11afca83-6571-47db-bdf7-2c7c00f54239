#!/usr/bin/env python3
"""
Volume Alert Commands for Discord
"""

import logging
import time
import discord
from discord.ext import commands
from discord import app_commands
from datetime import datetime, timezone

from services.market.volume_alert_service import get_volume_alert_service
from utils.config import get_watchlist_symbols

logger = logging.getLogger(__name__)

class VolumeCommands(commands.Cog):
    """Volume alert commands"""
    
    def __init__(self, bot):
        self.bot = bot
        self.volume_service = get_volume_alert_service()
    
    @app_commands.command(name="volume_status", description="Check volume alert service status")
    async def volume_status(self, interaction: discord.Interaction):
        """Check volume alert service status"""
        start_time = time.time()
        
        await interaction.response.defer()
        
        try:
            # Get service status
            is_running = self.volume_service.is_running
            watchlist_symbols = get_watchlist_symbols()
            
            # Create status embed
            embed = discord.Embed(
                title="📊 Volume Alert Service Status",
                color=0x00ff88 if is_running else 0xff4444,
                timestamp=datetime.now(timezone.utc)
            )
            
            # Service status
            status_emoji = "🟢" if is_running else "🔴"
            status_text = "Running" if is_running else "Stopped"
            embed.add_field(
                name="Service Status",
                value=f"{status_emoji} {status_text}",
                inline=True
            )
            
            # Configuration
            embed.add_field(
                name="Configuration",
                value=f"**Monitoring Interval:** {self.volume_service.monitoring_interval}s\n"
                      f"**MA Period:** {self.volume_service.ma_period}\n"
                      f"**Thresholds:** {', '.join(f'{t}x' for t in self.volume_service.thresholds)}",
                inline=True
            )
            
            # Watchlist symbols
            symbols_text = ", ".join(s.replace('USDT', '') for s in watchlist_symbols[:10])
            if len(watchlist_symbols) > 10:
                symbols_text += f" (+{len(watchlist_symbols) - 10} more)"
            
            embed.add_field(
                name=f"Watchlist Symbols ({len(watchlist_symbols)})",
                value=symbols_text or "None configured",
                inline=False
            )
            
            # Volume history status
            h4_symbols = len(self.volume_service.volume_history_h4)
            d1_symbols = len(self.volume_service.volume_history_d1)
            
            embed.add_field(
                name="Volume History",
                value=f"**H4 Data:** {h4_symbols} symbols\n"
                      f"**D1 Data:** {d1_symbols} symbols",
                inline=True
            )
            
            # Alert cooldowns
            active_cooldowns = len([k for k, v in self.volume_service.alert_cooldown.items() 
                                  if time.time() - v < self.volume_service.cooldown_duration])
            
            embed.add_field(
                name="Alert Cooldowns",
                value=f"**Active:** {active_cooldowns}\n"
                      f"**Duration:** {self.volume_service.cooldown_duration // 60}min",
                inline=True
            )
            
            embed.set_footer(text="Volume alerts monitor watchlist symbols for volume spikes")
            
            await interaction.followup.send(embed=embed)
            await self.bot.record_command_execution("volume_status", start_time, True)
            
        except Exception as e:
            logger.error(f"Error in volume_status command: {e}")
            await interaction.followup.send(
                f"❌ Error checking volume status: {str(e)}", 
                ephemeral=True
            )
            await self.bot.record_command_execution("volume_status", start_time, False)
    
    @app_commands.command(name="test_volume_alert", description="Send a test volume alert")
    @app_commands.describe(
        symbol="Symbol to test (must be in watchlist)",
        threshold="Threshold to simulate (1.8 or 2.2)"
    )
    async def test_volume_alert(self, interaction: discord.Interaction, 
                               symbol: str = "BTCUSDT", threshold: float = 2.2):
        """Send a test volume alert"""
        start_time = time.time()
        
        # Check permissions (admin only)
        if not interaction.user.guild_permissions.administrator:
            await interaction.response.send_message(
                "❌ This command requires administrator permissions.", 
                ephemeral=True
            )
            return
        
        await interaction.response.defer()
        
        try:
            # Validate symbol is in watchlist
            watchlist_symbols = get_watchlist_symbols()
            if symbol.upper() not in [s.upper() for s in watchlist_symbols]:
                await interaction.followup.send(
                    f"❌ Symbol {symbol} is not in watchlist. Available: {', '.join(watchlist_symbols)}", 
                    ephemeral=True
                )
                return
            
            # Validate threshold
            if threshold not in [1.8, 2.2]:
                await interaction.followup.send(
                    "❌ Threshold must be 1.8 or 2.2", 
                    ephemeral=True
                )
                return
            
            # Create test alert data
            test_alert = {
                'symbol': symbol.upper(),
                'timeframe': '4h',
                'current_volume': 1000000,
                'ma20_volume': 1000000 / threshold,
                'volume_ratio': threshold,
                'threshold': threshold,
                'timestamp': time.time(),
                'price': 50000.0  # Mock price
            }
            
            # Send test alert
            from handlers.discord.alerts.volume_alert_handler import get_volume_alert_handler
            handler = get_volume_alert_handler(self.bot)
            await handler.handle_volume_alert('volume_spike', {'alerts': [test_alert]})
            
            await interaction.followup.send(
                f"✅ Test volume alert sent for {symbol} with {threshold}x threshold", 
                ephemeral=True
            )
            await self.bot.record_command_execution("test_volume_alert", start_time, True)
            
        except Exception as e:
            logger.error(f"Error in test_volume_alert command: {e}")
            await interaction.followup.send(
                f"❌ Error sending test alert: {str(e)}", 
                ephemeral=True
            )
            await self.bot.record_command_execution("test_volume_alert", start_time, False)

async def setup(bot):
    await bot.add_cog(VolumeCommands(bot))
