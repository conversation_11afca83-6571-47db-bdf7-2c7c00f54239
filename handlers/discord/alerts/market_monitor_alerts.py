"""
Discord Market Monitor <PERSON><PERSON> - Sends market monitor alerts to <PERSON>rd and Telegram
"""

import logging
import discord
from utils.config import get_admin_id, get_guild_id
from utils.ui_components import format_percentage_enhanced
from utils.constants import EMOJI, DISCORD_FORMATTING

logger = logging.getLogger(__name__)

class MarketMonitorAlertHandler:
    """Handler for sending market monitor alerts to Discord only"""

    def __init__(self, bot):
        self.bot = bot
        self.admin_id = get_admin_id()
        self.guild_id = get_guild_id()

    async def send_market_alert(self, alert_type: str, alert_subtype: str,
                               current_rate: float, previous_rate: float, threshold: float):
        """Send market monitor alert to Discord only"""
        try:
            # Create alert embed based on type
            if alert_type == "earn_rate":
                embed = await self._create_earn_rate_embed(
                    alert_subtype, current_rate, previous_rate, threshold
                )
            else:
                logger.warning(f"Unknown alert type: {alert_type}")
                return

            # Send to guild channels (không gửi DM)
            if self.guild_id:
                try:
                    guild = self.bot.get_guild(int(self.guild_id))
                    if guild:

                        # Tìm channel alerts với emoji: 🚨alerts hoặc alerts
                        target_channel = None
                        for channel in guild.channels:
                            if hasattr(channel, 'name') and isinstance(channel, discord.TextChannel):
                                if 'alerts' in channel.name.lower():
                                    target_channel = channel
                                    break

                        # Fallback to general
                        if not target_channel:
                            for channel in guild.channels:
                                if hasattr(channel, 'name') and isinstance(channel, discord.TextChannel):
                                    if 'general' in channel.name.lower():
                                        target_channel = channel
                                        break

                        if target_channel:
                            await target_channel.send(embed=embed)
                            logger.info(f"Sent {alert_type} alert to #{target_channel.name}")
                        else:
                            logger.warning(f"No suitable channel found for {alert_type} alert")
                except Exception as e:
                    logger.error(f"Failed to send alert to guild: {e}")

        except Exception as e:
            logger.error(f"Error sending market monitor alert: {e}")

    async def _create_earn_rate_embed(self, alert_subtype: str, current_rate: float,
                                     previous_rate: float, threshold: float) -> discord.Embed:
        """Create simplified embed for earn rate alerts - only show current rate"""

        embed = discord.Embed(
            title=f"{EMOJI['chart']} LÃI SUẤT USDT FLEXIBLE EARN",
            description=f"Lãi suất hiện tại: **{current_rate:.2f}%** APY",
            color=DISCORD_FORMATTING['colors']['success'],
            timestamp=discord.utils.utcnow()
        )

        embed.set_footer(text=DISCORD_FORMATTING['footers']['alerts'])

        return embed



# Global handler instance
_market_monitor_handler = None

def get_market_monitor_alert_handler(bot) -> MarketMonitorAlertHandler:
    """Get or create market monitor alert handler"""
    global _market_monitor_handler
    if _market_monitor_handler is None:
        _market_monitor_handler = MarketMonitorAlertHandler(bot)
    return _market_monitor_handler
