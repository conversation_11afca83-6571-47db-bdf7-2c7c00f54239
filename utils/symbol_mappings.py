import logging
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

COINGECKO_SYMBOL_MAPPINGS: Dict[str, str] = {
    'btc': 'bitcoin',
    'eth': 'ethereum',
    'bnb': 'binancecoin',
    'ada': 'cardano',
    'sol': 'solana',
    'dot': 'polkadot',
    'link': 'chainlink',
    'matic': 'matic-network',
    'avax': 'avalanche-2',
    'atom': 'cosmos',
    'near': 'near',
    'ftm': 'fantom',
    'algo': 'algorand',
    'xlm': 'stellar',
    'vet': 'vechain',
    'icp': 'internet-computer',
    'fil': 'filecoin',
    'trx': 'tron',
    'etc': 'ethereum-classic',
    'xmr': 'monero',
    'bch': 'bitcoin-cash',
    'ltc': 'litecoin',
    'uni': 'uniswap',
    'doge': 'dogecoin',
    'shib': 'shiba-inu',
    'ena': 'ethena',
    'pepe': 'pepe',
    'wif': 'dogwifcoin',
    'bonk': 'bonk',
    'jup': 'jupiter-exchange-solana',
    'pyth': 'pyth-network',
    'jto': 'jito-governance-token',
    'sei': 'sei-network',
    'tia': 'celestia',
    'strk': 'starknet',
    'manta': 'manta-network',
    'alt': 'altlayer',
    'wen': 'wen-4',
    'bome': 'book-of-meme',
    'slerf': 'slerf',
    'w': 'wormhole',
    'tnsr': 'tensor',
    'saga': 'saga-2',
    'omni': 'omni-network',
    'rez': 'renzo',
    'io': 'io',
    'zkj': 'zkjump',
    'zro': 'layerzero',
    'g': 'gravity',
    'hmstr': 'hamster-kombat',
    'cati': 'catizen',
    'eigen': 'eigenlayer'
}

def get_coingecko_id(symbol: str) -> str:
    if not symbol or not isinstance(symbol, str):
        return ""

    base_symbol = symbol.upper().replace('USDT', '').replace('USDC', '').replace('BUSD', '')
    base_symbol = base_symbol.lower()

    coin_id = COINGECKO_SYMBOL_MAPPINGS.get(base_symbol, base_symbol)

    logger.debug(f"Symbol mapping: {symbol} -> {base_symbol} -> {coin_id}")
    return coin_id

def is_symbol_supported(symbol: str) -> bool:
    if not symbol or not isinstance(symbol, str):
        return False

    base_symbol = symbol.upper().replace('USDT', '').replace('USDC', '').replace('BUSD', '')
    base_symbol = base_symbol.lower()

    return base_symbol in COINGECKO_SYMBOL_MAPPINGS




